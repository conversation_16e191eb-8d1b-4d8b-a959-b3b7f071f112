# Tradovate Python SDK - Environment Configuration Template
# Copy this file to .env and update with your actual values

# =============================================================================
# API CREDENTIALS
# =============================================================================
# Your Tradovate API credentials
TRADOVATE_API_KEY=your_api_key_here
TRADOVATE_API_SECRET=your_api_secret_here
TRADOVATE_USERNAME=your_username_here
TRADOVATE_PASSWORD=your_password_here

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
# API environment: "demo" or "live"
TRADOVATE_ENVIRONMENT=demo

# Application identification
TRADOVATE_APP_ID=TradovateSDK
TRADOVATE_APP_VERSION=1.0.0

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# Request timeout in seconds (default: 30.0)
TRADOVATE_TIMEOUT=30.0

# Maximum requests per second (default: None for no limit)
TRADOVATE_RATE_LIMIT=10

# Maximum retry attempts (default: 3)
TRADOVATE_MAX_RETRIES=3

# Retry delay in seconds (default: 1.0)
TRADOVATE_RETRY_DELAY=1.0

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================
# WebSocket timeout in seconds (default: 30.0)
TRADOVATE_WEBSOCKET_TIMEOUT=30

# WebSocket ping interval in seconds (default: 20.0)
TRADOVATE_WEBSOCKET_PING_INTERVAL=20

# WebSocket ping timeout in seconds (default: 10.0)
TRADOVATE_WEBSOCKET_PING_TIMEOUT=10

# Auto-reconnect on connection loss (default: true)
TRADOVATE_WEBSOCKET_AUTO_RECONNECT=true

# Maximum reconnection attempts (default: 5)
TRADOVATE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS=5

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)
TRADOVATE_LOG_LEVEL=INFO

# Log file path (default: None for console only)
TRADOVATE_LOG_FILE=

# Enable structured logging (default: false)
TRADOVATE_STRUCTURED_LOGGING=false

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
# Enable caching (default: true)
TRADOVATE_ENABLE_CACHE=true

# Cache TTL in seconds (default: 300)
TRADOVATE_CACHE_TTL=300

# Maximum cache size (default: 1000)
TRADOVATE_CACHE_MAX_SIZE=1000

# =============================================================================
# TEST CONFIGURATION
# =============================================================================
# Test API credentials (for integration tests)
TRADOVATE_TEST_API_KEY=test_api_key
TRADOVATE_TEST_API_SECRET=test_api_secret
TRADOVATE_TEST_USERNAME=test_username
TRADOVATE_TEST_PASSWORD=test_password

# Test environment (default: demo)
TRADOVATE_TEST_ENVIRONMENT=demo

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable debug mode (default: false)
TRADOVATE_DEBUG=false

# Enable request/response logging (default: false)
TRADOVATE_LOG_REQUESTS=false

# Enable performance monitoring (default: false)
TRADOVATE_MONITOR_PERFORMANCE=false

# =============================================================================
# EXAMPLES CONFIGURATION
# =============================================================================
# Configuration for running examples

# Default contract to use in examples
TRADOVATE_EXAMPLE_CONTRACT=MES

# Default account ID for examples (leave empty to use first account)
TRADOVATE_EXAMPLE_ACCOUNT_ID=

# Example order quantity
TRADOVATE_EXAMPLE_QUANTITY=1

# Example order price offset from market (in ticks)
TRADOVATE_EXAMPLE_PRICE_OFFSET=5
