name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

jobs:
  test:
    name: Test Python ${{ matrix.python-version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        exclude:
          # Reduce matrix size for faster CI
          - os: windows-latest
            python-version: '3.8'
          - os: macos-latest
            python-version: '3.8'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"

    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=tradovate --cov-report=xml

    - name: Upload coverage to Codecov
      if: matrix.python-version == '3.11' && matrix.os == 'ubuntu-latest'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  lint:
    name: Code Quality Checks
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-lint-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-lint-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Run black
      run: black --check tradovate/ tests/ examples/

    - name: Run isort
      run: isort --check-only tradovate/ tests/ examples/

    - name: Run flake8
      run: flake8 tradovate/ tests/ examples/

    - name: Run mypy
      run: mypy tradovate/

    - name: Run bandit security checks
      run: bandit -r tradovate/ -f json

  docs:
    name: Documentation Build
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[docs]"

    - name: Build documentation
      run: |
        cd docs
        make html

    - name: Upload documentation artifacts
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/_build/html/

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"

    - name: Run integration tests
      env:
        TRADOVATE_TEST_API_KEY: ${{ secrets.TRADOVATE_TEST_API_KEY }}
        TRADOVATE_TEST_API_SECRET: ${{ secrets.TRADOVATE_TEST_API_SECRET }}
        TRADOVATE_TEST_USERNAME: ${{ secrets.TRADOVATE_TEST_USERNAME }}
        TRADOVATE_TEST_PASSWORD: ${{ secrets.TRADOVATE_TEST_PASSWORD }}
      run: |
        pytest tests/integration/ -v
      continue-on-error: true

  build:
    name: Build Distribution
    runs-on: ubuntu-latest
    needs: [test, lint]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build distribution
      run: python -m build

    - name: Check distribution
      run: twine check dist/*

    - name: Upload distribution artifacts
      uses: actions/upload-artifact@v3
      with:
        name: distribution
        path: dist/

  publish:
    name: Publish to PyPI
    runs-on: ubuntu-latest
    needs: [test, lint, build]
    if: github.event_name == 'release' && github.event.action == 'published'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Download distribution artifacts
      uses: actions/download-artifact@v3
      with:
        name: distribution
        path: dist/

    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        pip install twine
        twine upload dist/*
