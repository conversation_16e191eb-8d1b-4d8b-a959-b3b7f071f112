"""
Tests for the main client classes.
"""

import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch
from tradovate import TradovateClient, TradovateAsyncClient
from tradovate.enums import Environment
from tradovate.exceptions import ConfigurationError


class TestTradovateClient:
    """Test cases for TradovateClient."""
    
    def test_client_initialization(self):
        """Test client initialization with valid parameters."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret",
            environment=Environment.DEMO
        )
        
        assert client.api_key == "test_key"
        assert client.api_secret == "test_secret"
        assert client.environment == Environment.DEMO
        assert client.base_url == "https://demo.tradovateapi.com/v1"
        assert not client.is_authenticated
    
    def test_client_initialization_live_environment(self):
        """Test client initialization with live environment."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret",
            environment=Environment.LIVE
        )
        
        assert client.environment == Environment.LIVE
        assert client.base_url == "https://live.tradovateapi.com/v1"
    
    def test_client_initialization_missing_api_key(self):
        """Test client initialization with missing API key."""
        with pytest.raises(ConfigurationError):
            client = TradovateClient(
                api_key="",
                api_secret="test_secret"
            )
            client._validate_config()
    
    def test_client_initialization_missing_api_secret(self):
        """Test client initialization with missing API secret."""
        with pytest.raises(ConfigurationError):
            client = TradovateClient(
                api_key="test_key",
                api_secret=""
            )
            client._validate_config()
    
    def test_get_headers_without_auth(self):
        """Test getting headers without authentication."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        
        headers = client._get_headers(include_auth=False)
        
        assert "Content-Type" in headers
        assert "User-Agent" in headers
        assert "Authorization" not in headers
    
    def test_get_headers_with_auth(self):
        """Test getting headers with authentication."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        client.access_token = "test_token"
        
        headers = client._get_headers(include_auth=True)
        
        assert "Content-Type" in headers
        assert "User-Agent" in headers
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token"
    
    def test_is_authenticated_false_no_token(self):
        """Test is_authenticated returns False when no token."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        
        assert not client.is_authenticated
    
    def test_is_authenticated_false_expired_token(self):
        """Test is_authenticated returns False when token is expired."""
        from datetime import datetime, timedelta
        
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        client.access_token = "test_token"
        client.token_expires_at = datetime.now(timezone.utc) - timedelta(minutes=1)
        
        assert not client.is_authenticated
    
    def test_is_authenticated_true_valid_token(self):
        """Test is_authenticated returns True when token is valid."""
        from datetime import datetime, timedelta
        
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        client.access_token = "test_token"
        client.token_expires_at = datetime.now(timezone.utc) + timedelta(minutes=30)
        
        assert client.is_authenticated
    
    @patch('tradovate.client.httpx.Client')
    def test_client_context_manager(self, mock_http_client):
        """Test client as context manager."""
        mock_client_instance = Mock()
        mock_http_client.return_value = mock_client_instance
        
        with TradovateClient(api_key="test_key", api_secret="test_secret") as client:
            assert client is not None
        
        mock_client_instance.close.assert_called_once()


class TestTradovateAsyncClient:
    """Test cases for TradovateAsyncClient."""
    
    def test_async_client_initialization(self):
        """Test async client initialization."""
        client = TradovateAsyncClient(
            api_key="test_key",
            api_secret="test_secret",
            environment=Environment.DEMO
        )
        
        assert client.api_key == "test_key"
        assert client.api_secret == "test_secret"
        assert client.environment == Environment.DEMO
        assert client.http_client is None  # Not initialized until async context
    
    @pytest.mark.asyncio
    async def test_ensure_http_client(self):
        """Test HTTP client initialization in async context."""
        client = TradovateAsyncClient(
            api_key="test_key",
            api_secret="test_secret"
        )
        
        assert client.http_client is None
        
        await client._ensure_http_client()
        
        assert client.http_client is not None
        
        # Clean up
        await client.close()
    
    @pytest.mark.asyncio
    async def test_async_client_context_manager(self):
        """Test async client as context manager."""
        async with TradovateAsyncClient(
            api_key="test_key", 
            api_secret="test_secret"
        ) as client:
            assert client is not None
            assert client.http_client is not None


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = TradovateClient(
        api_key="test_key",
        api_secret="test_secret",
        environment=Environment.DEMO
    )
    client.http_client = Mock()
    return client


@pytest.fixture
def authenticated_client(mock_client):
    """Create an authenticated mock client."""
    from datetime import datetime, timedelta
    
    mock_client.access_token = "test_token"
    mock_client.user_id = 12345
    mock_client.token_expires_at = datetime.now(timezone.utc) + timedelta(minutes=30)
    
    return mock_client
