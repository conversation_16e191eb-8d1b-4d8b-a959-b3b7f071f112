"""
Integration tests for the Tradovate Python SDK.

These tests require valid API credentials and test against the actual
Tradovate API. They are designed to verify end-to-end functionality.
"""

import os
import asyncio
from typing import Dict, Any

import pytest

from tradovate.client import TradovateClient, TradovateAsyncClient
from tradovate.enums import Environment
from tradovate.exceptions import AuthenticationError, ValidationError
from tests.utils import skip_if_no_credentials, validate_api_response


@skip_if_no_credentials()
class TestClientIntegration:
    """Integration tests for the Tradovate client."""
    
    @classmethod
    def setup_class(cls):
        """Setup class with test credentials."""
        cls.api_key = os.getenv("TRADOVATE_TEST_API_KEY")
        cls.api_secret = os.getenv("TRADOVATE_TEST_API_SECRET")
        cls.username = os.getenv("TRADOVATE_TEST_USERNAME")
        cls.password = os.getenv("TRADOVATE_TEST_PASSWORD")
        cls.environment = Environment.DEMO  # Always use demo for tests
    
    def test_client_authentication(self):
        """Test client authentication with real API."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        # Authenticate
        response = client.authenticate(self.username, self.password)
        
        # Validate response
        validate_api_response(response, ["accessToken", "userId"])
        
        # Check client state
        assert client.is_authenticated
        assert client.access_token is not None
        assert client.user_id is not None
        
        # Clean up
        client.close()
    
    @pytest.mark.asyncio
    async def test_async_client_authentication(self):
        """Test async client authentication with real API."""
        async with TradovateAsyncClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        ) as client:
            # Authenticate
            response = await client.authenticate(self.username, self.password)
            
            # Validate response
            validate_api_response(response, ["accessToken", "userId"])
            
            # Check client state
            assert client.is_authenticated
            assert client.access_token is not None
            assert client.user_id is not None
    
    def test_get_user_accounts(self):
        """Test getting user accounts."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate first
            client.authenticate(self.username, self.password)
            
            # Get accounts
            accounts = client.accounts.get_accounts()
            
            # Validate response
            assert isinstance(accounts, list)
            if accounts:  # If user has accounts
                validate_api_response(accounts[0], ["id", "name", "accountType"])
        
        finally:
            client.close()
    
    def test_get_contracts(self):
        """Test getting contract information."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate first
            client.authenticate(self.username, self.password)
            
            # Get contracts
            contracts = client.contracts.get_contracts()
            
            # Validate response
            assert isinstance(contracts, list)
            if contracts:  # If contracts are available
                validate_api_response(contracts[0], ["id", "name"])
        
        finally:
            client.close()
    
    def test_rate_limiting(self):
        """Test that rate limiting works correctly."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment,
            rate_limit=2  # Very low rate limit for testing
        )
        
        try:
            # Authenticate first
            client.authenticate(self.username, self.password)
            
            # Make multiple rapid requests
            import time
            start_time = time.time()
            
            for _ in range(5):
                try:
                    client.accounts.get_accounts()
                except Exception:
                    pass  # Ignore errors, we're testing rate limiting
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Should take at least 2 seconds due to rate limiting
            # (5 requests at 2 per second = minimum 2.5 seconds)
            assert duration >= 2.0
        
        finally:
            client.close()
    
    def test_caching_functionality(self):
        """Test that caching works with real API calls."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Clear cache first
            client.cache.clear()
            
            # Authenticate
            client.authenticate(self.username, self.password)
            
            # Make the same request twice
            import time
            
            start_time = time.time()
            accounts1 = client.accounts.get_accounts()
            first_call_time = time.time() - start_time
            
            start_time = time.time()
            accounts2 = client.accounts.get_accounts()
            second_call_time = time.time() - start_time
            
            # Results should be the same
            assert accounts1 == accounts2
            
            # Note: Caching may not always make the second call faster
            # due to network variability, but we can check cache stats
            cache_stats = client.cache.get_stats()
            # Should have some cache activity
            assert cache_stats["total_requests"] >= 0
        
        finally:
            client.close()
    
    def test_performance_monitoring(self):
        """Test that performance monitoring works with real API calls."""
        from tradovate.utils import get_performance_stats, clear_performance_stats
        
        clear_performance_stats()
        
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate and make some API calls
            client.authenticate(self.username, self.password)
            client.accounts.get_accounts()
            
            # Check performance stats
            stats = get_performance_stats()
            
            # Should have recorded some performance data
            assert len(stats) > 0
            
            # Check that we have stats for authentication
            auth_stats = None
            for operation, op_stats in stats.items():
                if "auth" in operation.lower():
                    auth_stats = op_stats
                    break
            
            if auth_stats:
                assert auth_stats["count"] >= 1
                assert auth_stats["avg_duration"] > 0
        
        finally:
            client.close()
    
    def test_error_handling(self):
        """Test error handling with invalid credentials."""
        client = TradovateClient(
            api_key="invalid_key",
            api_secret="invalid_secret",
            environment=self.environment
        )
        
        try:
            with pytest.raises(AuthenticationError):
                client.authenticate("invalid_user", "invalid_password")
        finally:
            client.close()
    
    @pytest.mark.asyncio
    async def test_async_performance(self):
        """Test async client performance."""
        async with TradovateAsyncClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        ) as client:
            # Authenticate
            await client.authenticate(self.username, self.password)
            
            # Make concurrent requests
            tasks = []
            for _ in range(3):
                task = asyncio.create_task(client.accounts.get_accounts())
                tasks.append(task)
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All requests should succeed (or at least not raise exceptions)
            for result in results:
                if isinstance(result, Exception):
                    pytest.fail(f"Async request failed: {result}")
    
    def test_connection_pooling(self):
        """Test that connection pooling works correctly."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate
            client.authenticate(self.username, self.password)
            
            # Make multiple requests to test connection reuse
            for _ in range(5):
                try:
                    client.accounts.get_accounts()
                except Exception:
                    pass  # Ignore errors, we're testing connection pooling
            
            # Check that HTTP client is still active
            assert client.http_client is not None
            assert not client.http_client.is_closed
        
        finally:
            client.close()
            # After closing, client should be closed
            assert client.http_client.is_closed


@skip_if_no_credentials()
class TestWebSocketIntegration:
    """Integration tests for WebSocket functionality."""
    
    @classmethod
    def setup_class(cls):
        """Setup class with test credentials."""
        cls.api_key = os.getenv("TRADOVATE_TEST_API_KEY")
        cls.api_secret = os.getenv("TRADOVATE_TEST_API_SECRET")
        cls.username = os.getenv("TRADOVATE_TEST_USERNAME")
        cls.password = os.getenv("TRADOVATE_TEST_PASSWORD")
        cls.environment = Environment.DEMO
    
    @pytest.mark.asyncio
    async def test_websocket_connection(self):
        """Test WebSocket connection and basic functionality."""
        async with TradovateAsyncClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        ) as client:
            # Authenticate first
            await client.authenticate(self.username, self.password)
            
            # Get WebSocket client
            ws_client = client.websocket
            
            try:
                # Connect to WebSocket
                await ws_client.connect()
                
                # Check connection status
                assert ws_client.is_connected
                
                # Test ping/pong
                await ws_client.ping()
                
                # Disconnect
                await ws_client.disconnect()
                
                # Check disconnection
                assert not ws_client.is_connected
            
            except Exception as e:
                # WebSocket tests may fail due to network issues
                # or API limitations, so we'll just log the error
                pytest.skip(f"WebSocket test skipped due to: {e}")


@skip_if_no_credentials()
class TestAPIModulesIntegration:
    """Integration tests for various API modules."""
    
    @classmethod
    def setup_class(cls):
        """Setup class with test credentials."""
        cls.api_key = os.getenv("TRADOVATE_TEST_API_KEY")
        cls.api_secret = os.getenv("TRADOVATE_TEST_API_SECRET")
        cls.username = os.getenv("TRADOVATE_TEST_USERNAME")
        cls.password = os.getenv("TRADOVATE_TEST_PASSWORD")
        cls.environment = Environment.DEMO
    
    def test_lazy_loading_api_modules(self):
        """Test that API modules are loaded lazily."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate
            client.authenticate(self.username, self.password)
            
            # Access various API modules (should trigger lazy loading)
            assert hasattr(client, 'accounts')
            assert hasattr(client, 'contracts')
            assert hasattr(client, 'orders')
            assert hasattr(client, 'positions')
            
            # Extended APIs should also be available
            assert hasattr(client, 'currency')
            assert hasattr(client, 'products')
            
            # Accessing them should work
            try:
                client.currency.get_currencies()
            except Exception:
                pass  # May fail due to permissions, but should not raise AttributeError
        
        finally:
            client.close()
    
    def test_api_module_functionality(self):
        """Test basic functionality of API modules."""
        client = TradovateClient(
            api_key=self.api_key,
            api_secret=self.api_secret,
            environment=self.environment
        )
        
        try:
            # Authenticate
            client.authenticate(self.username, self.password)
            
            # Test accounts API
            accounts = client.accounts.get_accounts()
            assert isinstance(accounts, list)
            
            # Test contracts API
            contracts = client.contracts.get_contracts()
            assert isinstance(contracts, list)
            
            # Test that we can access user info
            user_info = client.users.get_user_info()
            assert isinstance(user_info, dict)
            validate_api_response(user_info, ["id", "name"])
        
        finally:
            client.close()
