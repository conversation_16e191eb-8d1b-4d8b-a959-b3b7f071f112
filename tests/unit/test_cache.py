"""
Cache tests for the Tradovate Python SDK.

These tests verify that the caching system works correctly and provides
the expected performance benefits.
"""

import time
import asyncio
from unittest.mock import Mock

import pytest

from tradovate.utils.cache import (
    CacheEntry, SimpleCache, cached, get_global_cache,
    clear_global_cache, get_cache_stats
)


class TestCacheEntry:
    """Test the CacheEntry class."""
    
    def test_cache_entry_creation(self):
        """Test creating a cache entry."""
        entry = CacheEntry("test_value", 60.0)
        
        assert entry.value == "test_value"
        assert not entry.is_expired()
    
    def test_cache_entry_expiration(self):
        """Test cache entry expiration."""
        entry = CacheEntry("test_value", 0.01)  # 10ms TTL
        
        assert not entry.is_expired()
        
        time.sleep(0.02)  # Wait for expiration
        
        assert entry.is_expired()


class TestSimpleCache:
    """Test the SimpleCache class."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.cache = SimpleCache(default_ttl=60.0, max_size=100)
    
    def test_set_and_get(self):
        """Test basic set and get operations."""
        self.cache.set("key1", "value1")
        
        result = self.cache.get("key1")
        assert result == "value1"
    
    def test_get_nonexistent_key(self):
        """Test getting a non-existent key."""
        result = self.cache.get("nonexistent")
        assert result is None
    
    def test_cache_expiration(self):
        """Test that expired entries are not returned."""
        self.cache.set("key1", "value1", ttl=0.01)  # 10ms TTL
        
        # Should be available immediately
        assert self.cache.get("key1") == "value1"
        
        # Wait for expiration
        time.sleep(0.02)
        
        # Should be None after expiration
        assert self.cache.get("key1") is None
    
    def test_cache_statistics(self):
        """Test cache statistics tracking."""
        # Start with clean stats
        assert self.cache.hits == 0
        assert self.cache.misses == 0
        
        # Set a value
        self.cache.set("key1", "value1")
        
        # Get the value (hit)
        self.cache.get("key1")
        assert self.cache.hits == 1
        assert self.cache.misses == 0
        
        # Try to get non-existent value (miss)
        self.cache.get("key2")
        assert self.cache.hits == 1
        assert self.cache.misses == 1
        
        # Get stats
        stats = self.cache.get_stats()
        assert stats["hits"] == 1
        assert stats["misses"] == 1
        assert stats["hit_rate"] == 0.5
        assert stats["total_requests"] == 2
    
    def test_cache_size_limit(self):
        """Test that cache respects size limits."""
        small_cache = SimpleCache(default_ttl=60.0, max_size=3)
        
        # Fill cache to capacity
        small_cache.set("key1", "value1")
        small_cache.set("key2", "value2")
        small_cache.set("key3", "value3")
        
        stats = small_cache.get_stats()
        assert stats["size"] == 3
        
        # Add one more item (should evict oldest)
        small_cache.set("key4", "value4")
        
        stats = small_cache.get_stats()
        assert stats["size"] == 3
        assert stats["evictions"] >= 1
    
    def test_delete_key(self):
        """Test deleting a key from cache."""
        self.cache.set("key1", "value1")
        assert self.cache.get("key1") == "value1"
        
        # Delete the key
        result = self.cache.delete("key1")
        assert result is True
        assert self.cache.get("key1") is None
        
        # Try to delete non-existent key
        result = self.cache.delete("nonexistent")
        assert result is False
    
    def test_clear_cache(self):
        """Test clearing the entire cache."""
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.get("key1")  # Generate some stats
        
        assert self.cache.get_stats()["size"] == 2
        assert self.cache.hits > 0
        
        self.cache.clear()
        
        stats = self.cache.get_stats()
        assert stats["size"] == 0
        assert stats["hits"] == 0
        assert stats["misses"] == 0


class TestCachedDecorator:
    """Test the cached decorator."""
    
    def test_cached_function(self):
        """Test caching a function result."""
        call_count = 0
        
        @cached(ttl=60.0)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # First call
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1
        
        # Second call with same argument (should use cache)
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 1  # Function not called again
        
        # Call with different argument
        result3 = expensive_function(10)
        assert result3 == 20
        assert call_count == 2  # Function called again
    
    @pytest.mark.asyncio
    async def test_cached_async_function(self):
        """Test caching an async function result."""
        call_count = 0
        
        @cached(ttl=60.0)
        async def expensive_async_function(x):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.001)  # Simulate async work
            return x * 2
        
        # First call
        result1 = await expensive_async_function(5)
        assert result1 == 10
        assert call_count == 1
        
        # Second call with same argument (should use cache)
        result2 = await expensive_async_function(5)
        assert result2 == 10
        assert call_count == 1  # Function not called again
    
    def test_cached_function_with_custom_key(self):
        """Test cached decorator with custom key function."""
        call_count = 0
        
        def custom_key_func(*args, **kwargs):
            # Only use first argument for key
            return f"custom_key_{args[0]}"
        
        @cached(ttl=60.0, key_func=custom_key_func)
        def function_with_custom_key(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # First call
        result1 = function_with_custom_key(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # Second call with same first arg but different second arg
        # Should still use cache due to custom key function
        result2 = function_with_custom_key(1, 5)
        assert result2 == 3  # Returns cached result
        assert call_count == 1
    
    def test_cached_function_expiration(self):
        """Test that cached function results expire."""
        call_count = 0
        
        @cached(ttl=0.01)  # 10ms TTL
        def function_with_short_ttl(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # First call
        result1 = function_with_short_ttl(5)
        assert result1 == 10
        assert call_count == 1
        
        # Wait for cache to expire
        time.sleep(0.02)
        
        # Second call (should call function again)
        result2 = function_with_short_ttl(5)
        assert result2 == 10
        assert call_count == 2
    
    def test_cached_function_cache_access(self):
        """Test accessing the cache from a cached function."""
        @cached(ttl=60.0)
        def cached_function(x):
            return x * 2
        
        # Function should have a cache attribute
        assert hasattr(cached_function, 'cache')
        assert isinstance(cached_function.cache, SimpleCache)
        
        # Call function to populate cache
        cached_function(5)
        
        # Check cache stats
        stats = cached_function.cache.get_stats()
        assert stats["size"] == 1


class TestGlobalCache:
    """Test global cache functionality."""
    
    def test_get_global_cache(self):
        """Test getting the global cache instance."""
        cache1 = get_global_cache()
        cache2 = get_global_cache()
        
        # Should return the same instance
        assert cache1 is cache2
        assert isinstance(cache1, SimpleCache)
    
    def test_clear_global_cache(self):
        """Test clearing the global cache."""
        cache = get_global_cache()
        
        # Add some data
        cache.set("test_key", "test_value")
        assert cache.get("test_key") == "test_value"
        
        # Clear global cache
        clear_global_cache()
        
        # Data should be gone
        assert cache.get("test_key") is None
    
    def test_get_cache_stats(self):
        """Test getting global cache statistics."""
        clear_global_cache()  # Start clean
        
        cache = get_global_cache()
        cache.set("key1", "value1")
        cache.get("key1")  # Hit
        cache.get("key2")  # Miss
        
        stats = get_cache_stats()
        assert stats["hits"] == 1
        assert stats["misses"] == 1
        assert stats["size"] == 1


class TestCacheIntegration:
    """Test cache integration with other components."""
    
    def test_cache_with_client(self):
        """Test that client can access cache."""
        from tradovate.client import TradovateClient
        
        client = TradovateClient(api_key="test", api_secret="test")
        
        # Client should have cache property
        assert hasattr(client, 'cache')
        cache = client.cache
        assert isinstance(cache, SimpleCache)
        
        # Should be the global cache
        assert cache is get_global_cache()
    
    def test_cache_performance_benefit(self):
        """Test that cache provides performance benefit."""
        call_times = []
        
        @cached(ttl=60.0)
        def slow_function(x):
            start = time.time()
            time.sleep(0.01)  # Simulate slow operation
            end = time.time()
            call_times.append(end - start)
            return x * 2
        
        # First call (cache miss)
        result1 = slow_function(5)
        
        # Second call (cache hit)
        result2 = slow_function(5)
        
        assert result1 == result2 == 10
        assert len(call_times) == 1  # Function only called once
    
    def test_cache_thread_safety(self):
        """Test that cache operations are thread-safe."""
        import threading
        
        cache = SimpleCache()
        results = []
        
        def worker(thread_id):
            for i in range(10):
                key = f"thread_{thread_id}_key_{i}"
                value = f"thread_{thread_id}_value_{i}"
                cache.set(key, value)
                retrieved = cache.get(key)
                results.append(retrieved == value)
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All operations should have succeeded
        assert all(results)
        assert len(results) == 50  # 5 threads * 10 operations each
