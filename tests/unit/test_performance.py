"""
Performance tests for the Tradovate Python SDK.

These tests verify that performance monitoring and optimization features
work correctly and that the SDK meets performance requirements.
"""

import time
import asyncio
from unittest.mock import Mock, patch

import pytest

from tradovate.utils.performance import (
    PerformanceMonitor, PerformanceMetrics, PerformanceContext,
    monitor_performance, get_performance_monitor, get_performance_stats,
    clear_performance_stats, format_performance_report
)
from tradovate.utils.cache import SimpleCache, cached
from tradovate.client import TradovateClient, TradovateAsyncClient
from tests.utils import assert_performance, PerformanceTimer


class TestPerformanceMonitor:
    """Test the performance monitoring system."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.monitor = PerformanceMonitor(max_metrics=100)
    
    def test_record_metric(self):
        """Test recording a performance metric."""
        metric = PerformanceMetrics(
            operation="test_operation",
            start_time=1000.0,
            end_time=1001.5,
            duration=1.5,
            success=True
        )
        
        self.monitor.record_metric(metric)
        
        stats = self.monitor.get_stats("test_operation")
        assert stats["count"] == 1
        assert stats["total_duration"] == 1.5
        assert stats["min_duration"] == 1.5
        assert stats["max_duration"] == 1.5
        assert stats["errors"] == 0
        assert stats["avg_duration"] == 1.5
        assert stats["error_rate"] == 0.0
    
    def test_record_multiple_metrics(self):
        """Test recording multiple metrics for the same operation."""
        metrics = [
            PerformanceMetrics("test_op", 1000.0, 1001.0, 1.0, True),
            PerformanceMetrics("test_op", 1002.0, 1004.0, 2.0, True),
            PerformanceMetrics("test_op", 1005.0, 1005.5, 0.5, False, "Error")
        ]
        
        for metric in metrics:
            self.monitor.record_metric(metric)
        
        stats = self.monitor.get_stats("test_op")
        assert stats["count"] == 3
        assert stats["total_duration"] == 3.5
        assert stats["min_duration"] == 0.5
        assert stats["max_duration"] == 2.0
        assert stats["errors"] == 1
        assert stats["avg_duration"] == 3.5 / 3
        assert stats["error_rate"] == 1.0 / 3
    
    def test_get_recent_metrics(self):
        """Test getting recent metrics."""
        metrics = [
            PerformanceMetrics(f"op_{i}", 1000.0 + i, 1001.0 + i, 1.0, True)
            for i in range(5)
        ]
        
        for metric in metrics:
            self.monitor.record_metric(metric)
        
        recent = self.monitor.get_recent_metrics(3)
        assert len(recent) == 3
        assert recent[-1].operation == "op_4"
    
    def test_clear_metrics(self):
        """Test clearing all metrics."""
        metric = PerformanceMetrics("test_op", 1000.0, 1001.0, 1.0, True)
        self.monitor.record_metric(metric)
        
        assert len(self.monitor.get_recent_metrics(10)) == 1
        
        self.monitor.clear()
        
        assert len(self.monitor.get_recent_metrics(10)) == 0
        assert self.monitor.get_stats("test_op") == {}


class TestPerformanceContext:
    """Test the performance context manager."""
    
    def test_context_manager_success(self):
        """Test performance context manager with successful operation."""
        monitor = PerformanceMonitor()
        
        with PerformanceContext("test_operation", monitor):
            time.sleep(0.01)  # Small delay
        
        stats = monitor.get_stats("test_operation")
        assert stats["count"] == 1
        assert stats["errors"] == 0
        assert stats["min_duration"] >= 0.01
    
    def test_context_manager_with_exception(self):
        """Test performance context manager with exception."""
        monitor = PerformanceMonitor()
        
        with pytest.raises(ValueError):
            with PerformanceContext("test_operation", monitor):
                raise ValueError("Test error")
        
        stats = monitor.get_stats("test_operation")
        assert stats["count"] == 1
        assert stats["errors"] == 1


class TestPerformanceDecorator:
    """Test the performance monitoring decorator."""
    
    def test_sync_function_decorator(self):
        """Test decorator on synchronous function."""
        clear_performance_stats()
        
        @monitor_performance("test_sync_function")
        def test_function(x, y):
            time.sleep(0.01)
            return x + y
        
        result = test_function(1, 2)
        assert result == 3
        
        stats = get_performance_stats("test_sync_function")
        assert stats["count"] == 1
        assert stats["errors"] == 0
    
    @pytest.mark.asyncio
    async def test_async_function_decorator(self):
        """Test decorator on asynchronous function."""
        clear_performance_stats()
        
        @monitor_performance("test_async_function")
        async def test_async_function(x, y):
            await asyncio.sleep(0.01)
            return x + y
        
        result = await test_async_function(1, 2)
        assert result == 3
        
        stats = get_performance_stats("test_async_function")
        assert stats["count"] == 1
        assert stats["errors"] == 0
    
    def test_function_with_exception(self):
        """Test decorator with function that raises exception."""
        clear_performance_stats()
        
        @monitor_performance("test_error_function")
        def error_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            error_function()
        
        stats = get_performance_stats("test_error_function")
        assert stats["count"] == 1
        assert stats["errors"] == 1


class TestCachePerformance:
    """Test cache performance features."""
    
    def test_cache_hit_performance(self):
        """Test that cache hits are faster than cache misses."""
        
        @cached(ttl=60.0)
        def expensive_function(x):
            time.sleep(0.01)  # Simulate expensive operation
            return x * 2
        
        # First call (cache miss)
        with PerformanceTimer() as timer1:
            result1 = expensive_function(5)
        
        # Second call (cache hit)
        with PerformanceTimer() as timer2:
            result2 = expensive_function(5)
        
        assert result1 == result2 == 10
        assert timer2.elapsed < timer1.elapsed  # Cache hit should be faster
    
    def test_cache_statistics(self):
        """Test cache statistics collection."""
        cache = SimpleCache(default_ttl=60.0)
        
        # Add some data
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        
        # Get some data (hits)
        cache.get("key1")
        cache.get("key2")
        
        # Try to get non-existent data (misses)
        cache.get("key3")
        cache.get("key4")
        
        stats = cache.get_stats()
        assert stats["hits"] == 2
        assert stats["misses"] == 2
        assert stats["hit_rate"] == 0.5
        assert stats["size"] == 2


class TestClientPerformance:
    """Test client performance features."""
    
    def test_client_request_performance_monitoring(self):
        """Test that client requests are monitored for performance."""
        clear_performance_stats()

        client = TradovateClient(api_key="test", api_secret="test")

        # Test direct HTTP methods which have performance monitoring
        with patch.object(client.http_client, 'get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"test": "data"}
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            # Make a GET request
            client.get("/test")

        # Check that performance was monitored
        stats = get_performance_stats()
        assert len(stats) > 0  # Should have some performance data
        assert "GET /test" in stats
    
    def test_rate_limiter_performance(self):
        """Test rate limiter performance."""
        from tradovate.utils import RateLimiter
        
        rate_limiter = RateLimiter(max_requests=100, time_window=1.0)
        
        # Test rapid requests
        start_time = time.time()
        for _ in range(50):
            assert rate_limiter.acquire(timeout=1.0)
        end_time = time.time()
        
        # Should complete quickly
        assert end_time - start_time < 1.0


class TestPerformanceReporting:
    """Test performance reporting features."""
    
    def test_format_performance_report(self):
        """Test formatting performance report."""
        clear_performance_stats()
        
        # Add some test metrics
        monitor = get_performance_monitor()
        metrics = [
            PerformanceMetrics("operation1", 1000.0, 1001.0, 1.0, True),
            PerformanceMetrics("operation1", 1002.0, 1003.5, 1.5, True),
            PerformanceMetrics("operation2", 1004.0, 1004.2, 0.2, False, "Error")
        ]
        
        for metric in metrics:
            monitor.record_metric(metric)
        
        report = format_performance_report()
        
        assert "Performance Report" in report
        assert "operation1" in report
        assert "operation2" in report
        assert "Calls:" in report
        assert "Avg Duration:" in report
        assert "Error Rate:" in report
    
    def test_empty_performance_report(self):
        """Test performance report with no data."""
        clear_performance_stats()
        
        report = format_performance_report()
        assert "No performance data available" in report


class TestPerformanceRequirements:
    """Test that the SDK meets performance requirements."""
    
    @assert_performance(0.1)  # Should complete in under 100ms
    def test_client_initialization_performance(self):
        """Test that client initialization is fast."""
        client = TradovateClient(api_key="test", api_secret="test")
        assert client is not None
    
    @assert_performance(0.05)  # Should complete in under 50ms
    def test_cache_operations_performance(self):
        """Test that cache operations are fast."""
        cache = SimpleCache()
        
        # Test rapid cache operations
        for i in range(100):
            cache.set(f"key_{i}", f"value_{i}")
        
        for i in range(100):
            value = cache.get(f"key_{i}")
            assert value == f"value_{i}"
    
    @pytest.mark.asyncio
    @assert_performance(0.1)  # Should complete in under 100ms
    async def test_async_client_initialization_performance(self):
        """Test that async client initialization is fast."""
        async with TradovateAsyncClient(api_key="test", api_secret="test") as client:
            assert client is not None
