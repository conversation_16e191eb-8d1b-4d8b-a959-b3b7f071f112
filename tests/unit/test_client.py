"""
Unit tests for the Tradovate client classes.
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock, AsyncMock
import httpx

from tradovate import TradovateClient, TradovateAsyncClient
from tradovate.enums import Environment
from tradovate.exceptions import (
    TradovateError,
    AuthenticationError,
    ValidationError,
    NetworkError,
    ConfigurationError
)


@pytest.mark.unit
class TestTradovateClient:
    """Test cases for the synchronous TradovateClient."""
    
    def test_client_initialization_valid(self):
        """Test client initialization with valid parameters."""
        client = TradovateClient(
            api_key="test_key",
            api_secret="test_secret",
            environment=Environment.DEMO,
            app_id="TestApp",
            app_version="1.0.0"
        )
        
        assert client.api_key == "test_key"
        assert client.api_secret == "test_secret"
        assert client.environment == Environment.DEMO
        assert client.app_id == "TestApp"
        assert client.app_version == "1.0.0"
        assert not client.is_authenticated
        assert client.access_token is None
    
    def test_client_initialization_invalid_api_key(self):
        """Test client initialization with invalid API key."""
        with pytest.raises(ValidationError, match="API key is required"):
            TradovateClient(api_key="", api_secret="test_secret")
    
    def test_client_initialization_invalid_api_secret(self):
        """Test client initialization with invalid API secret."""
        with pytest.raises(ValidationError, match="API secret is required"):
            TradovateClient(api_key="test_key", api_secret="")
    
    def test_client_initialization_invalid_environment(self):
        """Test client initialization with invalid environment."""
        with pytest.raises(ValidationError):
            TradovateClient(
                api_key="test_key",
                api_secret="test_secret",
                environment="invalid"
            )
    
    def test_base_url_demo_environment(self):
        """Test base URL for demo environment."""
        client = TradovateClient(
            api_key="test",
            api_secret="test",
            environment=Environment.DEMO
        )
        assert "demo" in client.base_url
    
    def test_base_url_live_environment(self):
        """Test base URL for live environment."""
        client = TradovateClient(
            api_key="test",
            api_secret="test",
            environment=Environment.LIVE
        )
        assert "live" in client.base_url
    
    def test_headers_without_auth(self):
        """Test headers without authentication."""
        client = TradovateClient(api_key="test", api_secret="test")
        headers = client._get_headers(include_auth=False)
        
        assert "User-Agent" in headers
        assert "Content-Type" in headers
        assert "Authorization" not in headers
    
    def test_headers_with_auth(self):
        """Test headers with authentication."""
        client = TradovateClient(api_key="test", api_secret="test")
        client.access_token = "test_token"
        
        headers = client._get_headers(include_auth=True)
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token"
    
    @patch('tradovate.api.base.BaseAPI.post')
    def test_authentication_success(self, mock_post):
        """Test successful authentication."""
        mock_post.return_value = {
            "accessToken": "test_access_token",
            "userId": 12345,
            "expirationTime": 3600
        }

        client = TradovateClient(api_key="test", api_secret="test")
        result = client.authenticate("username", "password")

        assert result["accessToken"] == "test_access_token"
        assert client.is_authenticated
        assert client.access_token == "test_access_token"
        assert client.user_id == 12345
    
    @patch('tradovate.client.httpx.Client.post')
    def test_authentication_failure(self, mock_post):
        """Test authentication failure."""
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {"error": "Invalid credentials"}
        mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
            "401 Unauthorized", request=Mock(), response=mock_response
        )
        mock_post.return_value = mock_response
        
        client = TradovateClient(api_key="test", api_secret="test")
        
        with pytest.raises(AuthenticationError):
            client.authenticate("invalid", "credentials")
        
        assert not client.is_authenticated
        assert client.access_token is None
    
    @patch('tradovate.api.base.BaseAPI.post')
    def test_authentication_network_error(self, mock_post):
        """Test authentication with network error."""
        mock_post.side_effect = NetworkError("Connection failed")

        client = TradovateClient(api_key="test", api_secret="test")

        with pytest.raises(NetworkError):
            client.authenticate("username", "password")
    
    def test_api_modules_initialization(self):
        """Test that all API modules are properly initialized."""
        client = TradovateClient(api_key="test", api_secret="test")
        
        # Check that all API modules are available
        assert hasattr(client, 'auth')
        assert hasattr(client, 'accounts')
        assert hasattr(client, 'contracts')
        assert hasattr(client, 'orders')
        assert hasattr(client, 'positions')
        assert hasattr(client, 'market_data')
        assert hasattr(client, 'risk')
        assert hasattr(client, 'users')
        assert hasattr(client, 'alerts')
        assert hasattr(client, 'chat')
        
        # Check that modules have reference to client
        assert client.auth.client is client
        assert client.accounts.client is client
    
    def test_websocket_property(self):
        """Test WebSocket client property."""
        client = TradovateClient(api_key="test", api_secret="test")
        
        # WebSocket should be created on first access
        websocket1 = client.websocket
        websocket2 = client.websocket
        
        assert websocket1 is websocket2  # Should be the same instance
        assert websocket1.client is client
    
    @patch('tradovate.client.httpx.Client.get')
    def test_get_request(self, mock_get):
        """Test GET request method."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        client = TradovateClient(api_key="test", api_secret="test")
        client.access_token = "test_token"
        
        result = client.get("/test/endpoint")
        
        assert result == {"data": "test"}
        mock_get.assert_called_once()
    
    @patch('tradovate.client.httpx.Client.post')
    def test_post_request(self, mock_post):
        """Test POST request method."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        client = TradovateClient(api_key="test", api_secret="test")
        client.access_token = "test_token"
        
        result = client.post("/test/endpoint", data={"key": "value"})
        
        assert result == {"success": True}
        mock_post.assert_called_once()
    
    def test_close_method(self):
        """Test client close method."""
        client = TradovateClient(api_key="test", api_secret="test")
        
        # Mock the HTTP client
        client.http_client = Mock()
        
        client.close()
        
        client.http_client.close.assert_called_once()


@pytest.mark.unit
@pytest.mark.asyncio
class TestTradovateAsyncClient:
    """Test cases for the asynchronous TradovateAsyncClient."""
    
    async def test_async_client_initialization(self):
        """Test async client initialization."""
        client = TradovateAsyncClient(
            api_key="test_key",
            api_secret="test_secret",
            environment=Environment.DEMO
        )
        
        assert client.api_key == "test_key"
        assert client.api_secret == "test_secret"
        assert client.environment == Environment.DEMO
        assert not client.is_authenticated
    
    async def test_async_context_manager(self):
        """Test async client as context manager."""
        async with TradovateAsyncClient(
            api_key="test",
            api_secret="test"
        ) as client:
            assert client.http_client is not None
            assert isinstance(client.http_client, httpx.AsyncClient)
    
    @patch('tradovate.api.base.BaseAPI.post_async')
    async def test_async_authentication_success(self, mock_post):
        """Test successful async authentication."""
        mock_post.return_value = {
            "accessToken": "test_access_token",
            "userId": 12345,
            "expirationTime": 3600
        }

        async with TradovateAsyncClient(
            api_key="test",
            api_secret="test"
        ) as client:
            result = await client.authenticate("username", "password")

            assert result["accessToken"] == "test_access_token"
            assert client.is_authenticated
            assert client.access_token == "test_access_token"
    
    @patch('tradovate.client.httpx.AsyncClient.get')
    async def test_async_get_request(self, mock_get):
        """Test async GET request method."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": "test"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        async with TradovateAsyncClient(
            api_key="test",
            api_secret="test"
        ) as client:
            client.access_token = "test_token"
            
            result = await client.get_async("/test/endpoint")
            
            assert result == {"data": "test"}
            mock_get.assert_called_once()
    
    @patch('tradovate.client.httpx.AsyncClient.post')
    async def test_async_post_request(self, mock_post):
        """Test async POST request method."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        async with TradovateAsyncClient(
            api_key="test",
            api_secret="test"
        ) as client:
            client.access_token = "test_token"
            
            result = await client.post_async("/test/endpoint", data={"key": "value"})
            
            assert result == {"success": True}
            mock_post.assert_called_once()
    
    async def test_async_client_close(self):
        """Test async client close method."""
        client = TradovateAsyncClient(api_key="test", api_secret="test")
        
        # Initialize HTTP client
        await client._ensure_http_client()
        
        # Mock the HTTP client
        client.http_client.aclose = AsyncMock()
        
        await client.close()
        
        client.http_client.aclose.assert_called_once()


@pytest.mark.unit
class TestClientConfiguration:
    """Test client configuration and validation."""
    
    def test_timeout_configuration(self):
        """Test timeout configuration."""
        client = TradovateClient(
            api_key="test",
            api_secret="test",
            timeout=60.0
        )
        
        assert client.timeout == 60.0
    
    def test_rate_limit_configuration(self):
        """Test rate limit configuration."""
        client = TradovateClient(
            api_key="test",
            api_secret="test",
            rate_limit=5
        )
        
        assert client.rate_limiter.max_requests == 5
    
    def test_invalid_timeout(self):
        """Test invalid timeout configuration."""
        with pytest.raises(ValidationError):
            TradovateClient(
                api_key="test",
                api_secret="test",
                timeout=-1
            )
    
    def test_invalid_rate_limit(self):
        """Test invalid rate limit configuration."""
        with pytest.raises(ValidationError):
            TradovateClient(
                api_key="test",
                api_secret="test",
                rate_limit=0
            )
    
    def test_from_config_method(self):
        """Test creating client from configuration."""
        config_data = {
            "api_key": "test_key",
            "api_secret": "test_secret",
            "environment": "demo",
            "timeout": 30.0,
            "rate_limit": 10
        }
        
        with patch('tradovate.utils.Config.from_dict') as mock_config:
            mock_config.return_value = Mock(**config_data)
            
            client = TradovateClient.from_config(mock_config.return_value)
            
            assert client.api_key == "test_key"
            assert client.api_secret == "test_secret"
