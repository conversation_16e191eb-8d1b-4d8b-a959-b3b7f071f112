"""
Test utilities for the Tradovate Python SDK.

This module provides common utilities, fixtures, and helpers for testing
the SDK functionality.
"""

import json
import time
import asyncio
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta

import pytest
import httpx


class MockResponse:
    """Mock HTTP response for testing."""
    
    def __init__(
        self,
        json_data: Dict[str, Any],
        status_code: int = 200,
        headers: Optional[Dict[str, str]] = None
    ):
        self.json_data = json_data
        self.status_code = status_code
        self.headers = headers or {}
    
    def json(self) -> Dict[str, Any]:
        """Return JSON data."""
        return self.json_data
    
    def raise_for_status(self) -> None:
        """Raise HTTPStatusError if status indicates an error."""
        if self.status_code >= 400:
            raise httpx.HTTPStatusError(
                message=f"HTTP {self.status_code}",
                request=Mock(),
                response=self
            )


class MockAsyncResponse(MockResponse):
    """Mock async HTTP response for testing."""
    
    async def json(self) -> Dict[str, Any]:
        """Return JSON data asynchronously."""
        return self.json_data


def create_mock_client(authenticated: bool = True) -> Mock:
    """
    Create a mock Tradovate client for testing.
    
    Args:
        authenticated: Whether the client should appear authenticated
        
    Returns:
        Mock client instance
    """
    client = Mock()
    client.api_key = "test_api_key"
    client.api_secret = "test_api_secret"
    client.environment = "demo"
    client.base_url = "https://demo.tradovateapi.com/v1"
    client.timeout = 30.0
    client.is_authenticated = authenticated
    client.access_token = "test_access_token" if authenticated else None
    client.user_id = 12345 if authenticated else None
    client.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=1) if authenticated else None
    
    return client


def create_mock_async_client(authenticated: bool = True) -> AsyncMock:
    """
    Create a mock async Tradovate client for testing.
    
    Args:
        authenticated: Whether the client should appear authenticated
        
    Returns:
        Mock async client instance
    """
    client = AsyncMock()
    client.api_key = "test_api_key"
    client.api_secret = "test_api_secret"
    client.environment = "demo"
    client.base_url = "https://demo.tradovateapi.com/v1"
    client.timeout = 30.0
    client.is_authenticated = authenticated
    client.access_token = "test_access_token" if authenticated else None
    client.user_id = 12345 if authenticated else None
    client.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=1) if authenticated else None
    
    return client


def create_sample_auth_response() -> Dict[str, Any]:
    """Create a sample authentication response."""
    return {
        "accessToken": "sample_access_token",
        "userId": 12345,
        "expirationTime": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
        "userStatus": "Active",
        "name": "Test User",
        "email": "<EMAIL>"
    }


def create_sample_account() -> Dict[str, Any]:
    """Create a sample account data."""
    return {
        "id": 67890,
        "name": "Test Account",
        "accountType": "Customer",
        "active": True,
        "clearingHouseId": 1,
        "riskCategoryId": 1,
        "autoLiqProfileId": 1,
        "marginAccountType": "Speculator",
        "legalStatus": "Individual",
        "archived": False
    }


def create_sample_position() -> Dict[str, Any]:
    """Create a sample position data."""
    return {
        "id": 11111,
        "accountId": 67890,
        "contractId": 22222,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "tradeDate": {
            "year": 2024,
            "month": 1,
            "day": 15
        },
        "netPos": 2,
        "netPrice": 4500.25,
        "bought": 3,
        "boughtValue": 13501.50,
        "sold": 1,
        "soldValue": 4500.75,
        "prevPos": 0,
        "prevPrice": 0.0
    }


def create_sample_order() -> Dict[str, Any]:
    """Create a sample order data."""
    return {
        "id": 33333,
        "accountId": 67890,
        "contractId": 22222,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "orderType": "Market",
        "orderQty": 1,
        "action": "Buy",
        "orderStatus": "Working",
        "rejectReason": "",
        "text": "Test order"
    }


def create_sample_contract() -> Dict[str, Any]:
    """Create a sample contract data."""
    return {
        "id": 22222,
        "name": "E-mini S&P 500",
        "contractMaturityId": 44444,
        "status": "Active",
        "masterInstrumentId": 55555,
        "maturityDate": "2024-03-15",
        "contractSize": 50,
        "tickSize": 0.25,
        "tickValue": 12.50
    }


class PerformanceTimer:
    """Timer for measuring test performance."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """Start the timer."""
        self.start_time = time.time()
    
    def stop(self):
        """Stop the timer."""
        self.end_time = time.time()
    
    @property
    def elapsed(self) -> float:
        """Get elapsed time in seconds."""
        if self.start_time is None:
            return 0.0
        end = self.end_time or time.time()
        return end - self.start_time
    
    def __enter__(self):
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


def assert_performance(max_duration: float):
    """
    Decorator to assert that a test completes within a time limit.
    
    Args:
        max_duration: Maximum allowed duration in seconds
    """
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                with PerformanceTimer() as timer:
                    result = await func(*args, **kwargs)
                assert timer.elapsed <= max_duration, f"Test took {timer.elapsed:.3f}s, expected <= {max_duration}s"
                return result
            return async_wrapper
        else:
            def wrapper(*args, **kwargs):
                with PerformanceTimer() as timer:
                    result = func(*args, **kwargs)
                assert timer.elapsed <= max_duration, f"Test took {timer.elapsed:.3f}s, expected <= {max_duration}s"
                return result
            return wrapper
    return decorator


def create_test_config() -> Dict[str, Any]:
    """Create a test configuration dictionary."""
    return {
        "api_key": "test_api_key",
        "api_secret": "test_api_secret",
        "environment": "demo",
        "timeout": 30.0,
        "rate_limit": 10,
        "app_id": "TestSDK",
        "app_version": "1.0.0"
    }


def validate_api_response(response: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that an API response contains required fields.
    
    Args:
        response: API response to validate
        required_fields: List of required field names
        
    Raises:
        AssertionError: If required fields are missing
    """
    for field in required_fields:
        assert field in response, f"Required field '{field}' missing from response"
        assert response[field] is not None, f"Required field '{field}' is None"


def create_mock_websocket():
    """Create a mock WebSocket for testing."""
    ws = AsyncMock()
    ws.send = AsyncMock()
    ws.recv = AsyncMock()
    ws.close = AsyncMock()
    ws.ping = AsyncMock()
    ws.pong = AsyncMock()
    return ws


class MockRateLimiter:
    """Mock rate limiter for testing."""
    
    def __init__(self, max_requests: int = 10):
        self.max_requests = max_requests
        self.requests = []
    
    def acquire(self, timeout: float = None) -> bool:
        """Mock acquire method."""
        current_time = time.time()
        self.requests.append(current_time)
        # Keep only requests from the last second
        self.requests = [t for t in self.requests if current_time - t < 1.0]
        return len(self.requests) <= self.max_requests
    
    async def acquire_async(self, timeout: float = None) -> bool:
        """Mock async acquire method."""
        return self.acquire(timeout)


def skip_if_no_credentials():
    """Skip test if integration test credentials are not available."""
    import os
    
    required_vars = [
        "TRADOVATE_TEST_API_KEY",
        "TRADOVATE_TEST_API_SECRET",
        "TRADOVATE_TEST_USERNAME",
        "TRADOVATE_TEST_PASSWORD"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    return pytest.mark.skipif(
        bool(missing_vars),
        reason=f"Integration test credentials not available: {missing_vars}"
    )
