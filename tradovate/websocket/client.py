"""
WebSocket client for the Tradovate SDK.

This module provides real-time data streaming capabilities through WebSocket
connections to the Tradovate API.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from ..exceptions import WebSocketError, AuthenticationError
from ..utils import get_logger


class WebSocketClient:
    """
    WebSocket client for real-time data streaming.
    
    This client handles WebSocket connections to the Tradovate API for
    real-time market data, order updates, and other live events.
    """
    
    def __init__(self, tradovate_client) -> None:
        """
        Initialize the WebSocket client.
        
        Args:
            tradovate_client: The main Tradovate client instance
        """
        self.client = tradovate_client
        self.logger = get_logger("websocket")
        
        # WebSocket connection
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.is_connected = False
        self.is_running = False
        
        # Connection settings
        self.url = self._get_websocket_url()
        self.ping_interval = 20.0
        self.ping_timeout = 10.0
        self.auto_reconnect = True
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5.0
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Subscriptions
        self.subscriptions: Dict[str, Dict[str, Any]] = {}
        
        # Statistics
        self.messages_sent = 0
        self.messages_received = 0
        self.connection_attempts = 0
        self.last_ping_time: Optional[datetime] = None
        self.last_pong_time: Optional[datetime] = None
    
    def _get_websocket_url(self) -> str:
        """Get the WebSocket URL based on environment."""
        if self.client.environment.value == "demo":
            return "wss://demo.tradovateapi.com/v1/websocket"
        else:
            return "wss://live.tradovateapi.com/v1/websocket"
    
    def add_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Add an event handler for a specific event type.
        
        Args:
            event_type: Type of event to handle
            handler: Handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        self.logger.debug(f"Added event handler for {event_type}")
    
    def remove_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Remove an event handler.
        
        Args:
            event_type: Type of event
            handler: Handler function to remove
        """
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
                self.logger.debug(f"Removed event handler for {event_type}")
            except ValueError:
                pass
    
    def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        Emit an event to all registered handlers.
        
        Args:
            event_type: Type of event
            data: Event data
        """
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(data)
                except Exception as e:
                    self.logger.error(f"Error in event handler for {event_type}: {e}")
    
    async def connect(self) -> None:
        """
        Connect to the WebSocket server.
        
        Raises:
            WebSocketError: If connection fails
            AuthenticationError: If authentication is required
        """
        if not self.client.is_authenticated:
            raise AuthenticationError("Client must be authenticated before WebSocket connection")
        
        self.connection_attempts += 1
        self.logger.info(f"Connecting to WebSocket: {self.url}")
        
        try:
            # Add authentication headers
            headers = {
                "Authorization": f"Bearer {self.client.access_token}",
                "User-Agent": f"{self.client.app_id}/{self.client.app_version}",
            }
            
            self.websocket = await websockets.connect(
                self.url,
                extra_headers=headers,
                ping_interval=self.ping_interval,
                ping_timeout=self.ping_timeout,
            )
            
            self.is_connected = True
            self.logger.info("WebSocket connected successfully")
            self._emit_event("connect", {"url": self.url})
            
        except Exception as e:
            self.is_connected = False
            error_msg = f"WebSocket connection failed: {e}"
            self.logger.error(error_msg)
            raise WebSocketError(error_msg)
    
    async def disconnect(self) -> None:
        """Disconnect from the WebSocket server."""
        if self.websocket and self.is_connected:
            self.logger.info("Disconnecting WebSocket")
            self.is_running = False
            await self.websocket.close()
            self.is_connected = False
            self._emit_event("disconnect", {"reason": "Manual disconnect"})
    
    async def send_message(self, message: Dict[str, Any]) -> None:
        """
        Send a message to the WebSocket server.
        
        Args:
            message: Message to send
            
        Raises:
            WebSocketError: If not connected or send fails
        """
        if not self.is_connected or not self.websocket:
            raise WebSocketError("WebSocket not connected")
        
        try:
            message_json = json.dumps(message)
            await self.websocket.send(message_json)
            self.messages_sent += 1
            self.logger.debug(f"Sent message: {message.get('type', 'unknown')}")
        except Exception as e:
            error_msg = f"Failed to send message: {e}"
            self.logger.error(error_msg)
            raise WebSocketError(error_msg)
    
    async def _handle_message(self, message: str) -> None:
        """
        Handle incoming WebSocket message.
        
        Args:
            message: Raw message string
        """
        try:
            data = json.loads(message)
            self.messages_received += 1
            
            message_type = data.get("type", "unknown")
            self.logger.debug(f"Received message: {message_type}")
            
            # Emit event for the message type
            self._emit_event(message_type, data)
            
            # Handle specific message types
            if message_type == "quote":
                self._handle_quote_message(data)
            elif message_type == "trade":
                self._handle_trade_message(data)
            elif message_type == "depth":
                self._handle_depth_message(data)
            elif message_type == "order_update":
                self._handle_order_update(data)
            elif message_type == "position_update":
                self._handle_position_update(data)
            elif message_type == "error":
                self._handle_error_message(data)
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse message: {e}")
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
    
    def _handle_quote_message(self, data: Dict[str, Any]) -> None:
        """Handle quote message."""
        self._emit_event("quote", data)
    
    def _handle_trade_message(self, data: Dict[str, Any]) -> None:
        """Handle trade message."""
        self._emit_event("trade", data)
    
    def _handle_depth_message(self, data: Dict[str, Any]) -> None:
        """Handle depth of market message."""
        self._emit_event("depth", data)
    
    def _handle_order_update(self, data: Dict[str, Any]) -> None:
        """Handle order update message."""
        self._emit_event("order_update", data)
    
    def _handle_position_update(self, data: Dict[str, Any]) -> None:
        """Handle position update message."""
        self._emit_event("position_update", data)
    
    def _handle_error_message(self, data: Dict[str, Any]) -> None:
        """Handle error message."""
        error_msg = data.get("message", "Unknown WebSocket error")
        self.logger.error(f"WebSocket error: {error_msg}")
        self._emit_event("error", data)
    
    async def run(self) -> None:
        """
        Run the WebSocket client message loop.
        
        This method will run continuously, handling incoming messages
        and maintaining the connection.
        """
        if not self.is_connected:
            await self.connect()
        
        self.is_running = True
        self.logger.info("Starting WebSocket message loop")
        
        try:
            async for message in self.websocket:
                if not self.is_running:
                    break
                await self._handle_message(message)
                
        except ConnectionClosed as e:
            self.logger.warning(f"WebSocket connection closed: {e}")
            self.is_connected = False
            self._emit_event("disconnect", {"reason": str(e)})
            
            if self.auto_reconnect and self.is_running:
                await self._attempt_reconnect()
                
        except WebSocketException as e:
            self.logger.error(f"WebSocket error: {e}")
            self.is_connected = False
            self._emit_event("error", {"error": str(e)})
            
        except Exception as e:
            self.logger.error(f"Unexpected error in message loop: {e}")
            self.is_connected = False
            self._emit_event("error", {"error": str(e)})
        
        finally:
            self.is_running = False
    
    async def _attempt_reconnect(self) -> None:
        """Attempt to reconnect to the WebSocket server."""
        for attempt in range(self.max_reconnect_attempts):
            if not self.is_running:
                break
                
            self.logger.info(f"Reconnection attempt {attempt + 1}/{self.max_reconnect_attempts}")
            
            try:
                await asyncio.sleep(self.reconnect_delay)
                await self.connect()
                
                # Resubscribe to all previous subscriptions
                await self._resubscribe()
                
                # Restart message loop
                await self.run()
                return
                
            except Exception as e:
                self.logger.error(f"Reconnection attempt {attempt + 1} failed: {e}")
        
        self.logger.error("All reconnection attempts failed")
        self._emit_event("reconnect_failed", {})
    
    async def _resubscribe(self) -> None:
        """Resubscribe to all previous subscriptions after reconnection."""
        for sub_id, sub_data in self.subscriptions.items():
            try:
                await self.send_message(sub_data)
                self.logger.debug(f"Resubscribed to {sub_id}")
            except Exception as e:
                self.logger.error(f"Failed to resubscribe to {sub_id}: {e}")
    
    def start(self) -> None:
        """Start the WebSocket client (synchronous wrapper)."""
        asyncio.create_task(self.run())
    
    async def close(self) -> None:
        """Close the WebSocket client."""
        await self.disconnect()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket client statistics."""
        return {
            "is_connected": self.is_connected,
            "is_running": self.is_running,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "connection_attempts": self.connection_attempts,
            "subscriptions": len(self.subscriptions),
            "event_handlers": {k: len(v) for k, v in self.event_handlers.items()},
            "last_ping_time": self.last_ping_time,
            "last_pong_time": self.last_pong_time,
        }
