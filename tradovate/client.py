"""
Main client classes for the Tradovate SDK.

This module provides the primary interface for interacting with the Tradovate API,
including both synchronous and asynchronous clients.
"""

from typing import Optional, Dict, Any, Union
import httpx
from datetime import datetime, timezone

from .enums import Environment
from .exceptions import ConfigurationError
from .utils import Config, get_logger, RateLimiter
from .api import (
    AuthAPI,
    AccountsAPI,
    ContractsAPI,
    OrdersAPI,
    PositionsAPI,
    MarketDataAPI,
    RiskAPI,
    UsersAPI,
    AlertsAPI,
    ChatAPI,
)
from .websocket import WebSocketClient


class BaseClient:
    """Base client class with common functionality."""
    
    def __init__(
        self,
        api_key: str,
        api_secret: str,
        environment: Union[Environment, str] = Environment.DEMO,
        app_id: str = "TradovateSDK",
        app_version: str = "1.0.0",
        timeout: float = 30.0,
        rate_limit: Optional[int] = None,
        **kwargs
    ) -> None:
        """
        Initialize the base client.
        
        Args:
            api_key: Your Tradovate API key
            api_secret: Your Tradovate API secret
            environment: API environment ("demo" or "live")
            app_id: Application identifier
            app_version: Application version
            timeout: Request timeout in seconds
            rate_limit: Maximum requests per second (None for no limit)
            **kwargs: Additional configuration options
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.environment = Environment(environment)
        self.app_id = app_id
        self.app_version = app_version
        self.timeout = timeout
        
        # Set base URL based on environment
        if self.environment == Environment.DEMO:
            self.base_url = "https://demo.tradovateapi.com/v1"
        else:
            self.base_url = "https://live.tradovateapi.com/v1"
        
        # Initialize rate limiter if specified
        self.rate_limiter = RateLimiter(rate_limit) if rate_limit else None
        
        # Authentication state
        self.access_token: Optional[str] = None
        self.md_access_token: Optional[str] = None
        self.user_id: Optional[int] = None
        self.token_expires_at: Optional[datetime] = None
        
        # Logger
        self.logger = get_logger(__name__)

        # Configuration
        self.config = Config(**kwargs)

        self.logger.info(f"Initialized Tradovate client for {environment} environment")

    def _initialize_api_modules(self) -> None:
        """Initialize all API modules. Shared between sync and async clients."""
        # Initialize core API modules
        self.auth = AuthAPI(self)
        self.accounts = AccountsAPI(self)
        self.contracts = ContractsAPI(self)
        self.orders = OrdersAPI(self)
        self.positions = PositionsAPI(self)
        self.market_data = MarketDataAPI(self)
        self.risk = RiskAPI(self)
        self.users = UsersAPI(self)
        self.alerts = AlertsAPI(self)
        self.chat = ChatAPI(self)

        # Initialize extended API modules
        self._initialize_extended_apis()

        # WebSocket client (initialized when needed)
        self._websocket_client: Optional[WebSocketClient] = None

    def _initialize_extended_apis(self) -> None:
        """Initialize extended API modules."""
        # Import here to avoid circular imports
        from .api.currency import CurrencyAPI, CurrencyRateAPI
        from .api.products import ProductsAPI, ProductSessionAPI, SpreadDefinitionAPI
        from .api.commands import CommandsAPI, CommandReportsAPI
        from .api.positions import FillPairAPI, FillFeeAPI
        from .api.cash_balance_log import CashBalanceLogAPI, MarginSnapshotAPI
        from .api.market_data_subscriptions import MarketDataSubscriptionAPI, SecondMarketDataSubscriptionAPI
        from .api.administrative import ClearingHouseAPI, EntitlementAPI, PropertyAPI, OrganizationAPI
        from .api.replay import ReplaySessionAPI

        # Currency and exchange APIs
        self.currency = CurrencyAPI(self)
        self.currency_rate = CurrencyRateAPI(self)

        # Product APIs
        self.products = ProductsAPI(self)
        self.product_session = ProductSessionAPI(self)
        self.spread_definition = SpreadDefinitionAPI(self)

        # Command APIs
        self.commands = CommandsAPI(self)
        self.command_reports = CommandReportsAPI(self)

        # Extended position and fill APIs
        self.fill_pair = FillPairAPI(self)
        self.fill_fee = FillFeeAPI(self)

        # Account management APIs
        self.cash_balance_log = CashBalanceLogAPI(self)
        self.margin_snapshot = MarginSnapshotAPI(self)

        # Market data subscription APIs
        self.market_data_subscription = MarketDataSubscriptionAPI(self)
        self.second_market_data_subscription = SecondMarketDataSubscriptionAPI(self)

        # Administrative APIs
        self.clearing_house = ClearingHouseAPI(self)
        self.entitlement = EntitlementAPI(self)
        self.property = PropertyAPI(self)
        self.organization = OrganizationAPI(self)

        # Replay APIs
        self.replay_session = ReplaySessionAPI(self)
    
    @property
    def is_authenticated(self) -> bool:
        """Check if the client is currently authenticated."""
        return (
            self.access_token is not None and
            self.token_expires_at is not None and
            datetime.now(timezone.utc) < self.token_expires_at
        )
    
    def _get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """Get HTTP headers for API requests."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"{self.app_id}/{self.app_version}",
        }
        
        if include_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    def _validate_config(self) -> None:
        """Validate client configuration."""
        if not self.api_key:
            raise ConfigurationError("API key is required")
        if not self.api_secret:
            raise ConfigurationError("API secret is required")


class TradovateClient(BaseClient):
    """
    Synchronous Tradovate API client.
    
    This client provides a synchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        # Initialize HTTP client
        self.http_client = httpx.Client(
            timeout=self.timeout,
            headers=self._get_headers(include_auth=False)
        )

        # Initialize all API modules using shared method
        self._initialize_api_modules()
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        return self.auth.authenticate(username, password)
    
    def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.
        
        Returns:
            Token renewal response data
            
        Raises:
            AuthenticationError: If token renewal fails
        """
        return self.auth.renew_token()
    
    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            self.http_client.close()
        if self._websocket_client:
            self._websocket_client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager and close client."""
        self.close()


class TradovateAsyncClient(BaseClient):
    """
    Asynchronous Tradovate API client.
    
    This client provides an asynchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        # HTTP client will be initialized in async context
        self.http_client: Optional[httpx.AsyncClient] = None

        # Initialize all API modules using shared method
        self._initialize_api_modules()
    
    async def _ensure_http_client(self) -> None:
        """Ensure HTTP client is initialized."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(
                timeout=self.timeout,
                headers=self._get_headers(include_auth=False)
            )
    
    async def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        await self._ensure_http_client()
        return await self.auth.authenticate(username, password)
    
    async def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.
        
        Returns:
            Token renewal response data
            
        Raises:
            AuthenticationError: If token renewal fails
        """
        await self._ensure_http_client()
        return await self.auth.renew_token()
    
    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    async def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            await self.http_client.aclose()
        if self._websocket_client:
            await self._websocket_client.close()
    
    async def __aenter__(self):
        await self._ensure_http_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context manager and close client."""
        await self.close()
