"""
Main client classes for the Tradovate SDK.

This module provides the primary interface for interacting with the Tradovate API,
including both synchronous and asynchronous clients.
"""

from typing import Optional, Dict, Any, Union
import httpx
from datetime import datetime, timezone

from .enums import Environment
from .exceptions import ConfigurationError, ValidationError, NetworkError, AuthenticationError
from .utils import Config, get_logger, RateLimiter, get_global_cache, PerformanceContext
from .api import (
    AuthAPI,
    AccountsAPI,
    ContractsAPI,
    OrdersAPI,
    PositionsAPI,
    MarketDataAPI,
    RiskAPI,
    UsersAPI,
    AlertsAPI,
    ChatAPI,
)
from .websocket import WebSocketClient


class BaseClient:
    """Base client class with common functionality."""
    
    def __init__(
        self,
        api_key: str,
        api_secret: str,
        environment: Union[Environment, str] = Environment.DEMO,
        app_id: str = "TradovateSDK",
        app_version: str = "1.0.0",
        timeout: float = 30.0,
        rate_limit: Optional[int] = None,
        **kwargs
    ) -> None:
        """
        Initialize the base client.

        Args:
            api_key: Your Tradovate API key
            api_secret: Your Tradovate API secret
            environment: API environment ("demo" or "live")
            app_id: Application identifier
            app_version: Application version
            timeout: Request timeout in seconds
            rate_limit: Maximum requests per second (None for no limit)
            **kwargs: Additional configuration options
        """
        # Validate required parameters
        if not api_key or not api_key.strip():
            raise ValidationError("API key is required")
        if not api_secret or not api_secret.strip():
            raise ValidationError("API secret is required")
        if timeout <= 0:
            raise ValidationError("Timeout must be positive")
        if rate_limit is not None and rate_limit <= 0:
            raise ValidationError("Rate limit must be positive")

        self.api_key = api_key
        self.api_secret = api_secret
        try:
            self.environment = Environment(environment)
        except ValueError as e:
            raise ValidationError(f"Invalid environment: {e}")
        self.app_id = app_id
        self.app_version = app_version
        self.timeout = timeout
        
        # Set base URL based on environment
        if self.environment == Environment.DEMO:
            self.base_url = "https://demo.tradovateapi.com/v1"
        else:
            self.base_url = "https://live.tradovateapi.com/v1"
        
        # Initialize rate limiter if specified
        self.rate_limiter = RateLimiter(rate_limit) if rate_limit else None
        
        # Authentication state
        self.access_token: Optional[str] = None
        self.md_access_token: Optional[str] = None
        self.user_id: Optional[int] = None
        self.token_expires_at: Optional[datetime] = None
        
        # Logger
        self.logger = get_logger(__name__)

        # Configuration
        self.config = Config(**kwargs)

        self.logger.info(f"Initialized Tradovate client for {environment} environment")

    def _initialize_api_modules(self) -> None:
        """Initialize all API modules. Shared between sync and async clients."""
        # Initialize core API modules
        self.auth = AuthAPI(self)
        self.accounts = AccountsAPI(self)
        self.contracts = ContractsAPI(self)
        self.orders = OrdersAPI(self)
        self.positions = PositionsAPI(self)
        self.market_data = MarketDataAPI(self)
        self.risk = RiskAPI(self)
        self.users = UsersAPI(self)
        self.alerts = AlertsAPI(self)
        self.chat = ChatAPI(self)

        # Initialize extended API modules
        self._initialize_extended_apis()

        # WebSocket client (initialized when needed)
        self._websocket_client: Optional[WebSocketClient] = None

    @property
    def cache(self):
        """Get the global cache instance."""
        return get_global_cache()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for this client.

        Returns:
            Performance statistics dictionary
        """
        from .utils import get_performance_stats
        return get_performance_stats()

    def clear_performance_stats(self) -> None:
        """Clear performance statistics."""
        from .utils import clear_performance_stats
        clear_performance_stats()

    @classmethod
    def from_config(cls, config: Config):
        """
        Create a client instance from a configuration object.

        Args:
            config: Configuration object containing client settings

        Returns:
            Client instance
        """
        return cls(
            api_key=config.api_key,
            api_secret=config.api_secret,
            environment=config.environment,
            app_id=getattr(config, 'app_id', 'TradovateSDK'),
            app_version=getattr(config, 'app_version', '1.0.0'),
            timeout=getattr(config, 'timeout', 30.0),
            rate_limit=getattr(config, 'rate_limit', None)
        )

    def _initialize_extended_apis(self) -> None:
        """Initialize extended API modules with lazy loading."""
        # Store lazy-loaded API modules
        self._extended_apis = {}

        # Define API module mappings for lazy loading
        self._api_mappings = {
            'currency': ('tradovate.api.currency', 'CurrencyAPI'),
            'currency_rate': ('tradovate.api.currency', 'CurrencyRateAPI'),
            'products': ('tradovate.api.products', 'ProductsAPI'),
            'product_session': ('tradovate.api.products', 'ProductSessionAPI'),
            'spread_definition': ('tradovate.api.products', 'SpreadDefinitionAPI'),
            'commands': ('tradovate.api.commands', 'CommandsAPI'),
            'command_reports': ('tradovate.api.commands', 'CommandReportsAPI'),
            'fill_pair': ('tradovate.api.positions', 'FillPairAPI'),
            'fill_fee': ('tradovate.api.positions', 'FillFeeAPI'),
            'cash_balance_log': ('tradovate.api.cash_balance_log', 'CashBalanceLogAPI'),
            'margin_snapshot': ('tradovate.api.cash_balance_log', 'MarginSnapshotAPI'),
            'market_data_subscription': ('tradovate.api.market_data_subscriptions', 'MarketDataSubscriptionAPI'),
            'second_market_data_subscription': ('tradovate.api.market_data_subscriptions', 'SecondMarketDataSubscriptionAPI'),
            'clearing_house': ('tradovate.api.administrative', 'ClearingHouseAPI'),
            'entitlement': ('tradovate.api.administrative', 'EntitlementAPI'),
            'property': ('tradovate.api.administrative', 'PropertyAPI'),
            'organization': ('tradovate.api.administrative', 'OrganizationAPI'),
            'replay_session': ('tradovate.api.replay', 'ReplaySessionAPI'),
        }

    def __getattr__(self, name: str):
        """Lazy load extended API modules when accessed."""
        if name in self._api_mappings:
            if name not in self._extended_apis:
                module_path, class_name = self._api_mappings[name]
                try:
                    import importlib
                    module = importlib.import_module(module_path)
                    api_class = getattr(module, class_name)
                    self._extended_apis[name] = api_class(self)
                    self.logger.debug(f"Lazy loaded API module: {name}")
                except ImportError as e:
                    self.logger.warning(f"Failed to load API module {name}: {e}")
                    raise AttributeError(f"API module '{name}' not available")

            return self._extended_apis[name]

        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    @property
    def is_authenticated(self) -> bool:
        """Check if the client is currently authenticated."""
        return (
            self.access_token is not None and
            self.token_expires_at is not None and
            datetime.now(timezone.utc) < self.token_expires_at
        )
    
    def _get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """Get HTTP headers for API requests."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"{self.app_id}/{self.app_version}",
        }
        
        if include_auth and self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        return headers
    
    def _validate_config(self) -> None:
        """Validate client configuration."""
        if not self.api_key:
            raise ConfigurationError("API key is required")
        if not self.api_secret:
            raise ConfigurationError("API secret is required")


class TradovateClient(BaseClient):
    """
    Synchronous Tradovate API client.
    
    This client provides a synchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        # Initialize HTTP client with connection pooling
        self.http_client = httpx.Client(
            timeout=self.timeout,
            headers=self._get_headers(include_auth=False),
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=100,
                keepalive_expiry=30.0
            ),
            http2=True  # Enable HTTP/2 for better performance
        )

        # Initialize all API modules using shared method
        self._initialize_api_modules()
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        return self.auth.authenticate(username, password)
    
    def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.

        Returns:
            Token renewal response data

        Raises:
            AuthenticationError: If token renewal fails
        """
        return self.auth.renew_token()

    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make a GET request to the API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Response data
        """
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(include_auth=True)

        # Apply rate limiting if configured
        if self.rate_limiter:
            if not self.rate_limiter.acquire(timeout=30.0):
                raise NetworkError("Rate limit exceeded")

        self.logger.debug(f"GET {url} with params: {params}")

        with PerformanceContext(f"GET {endpoint}"):
            try:
                response = self.http_client.get(url, params=params, headers=headers)
                self.logger.debug(f"Response status: {response.status_code}")
                response.raise_for_status()

                result = response.json()
                self.logger.debug(f"Response data keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                return result

            except httpx.HTTPStatusError as e:
                self.logger.error(f"HTTP error {e.response.status_code}: {e}")
                if e.response.status_code == 401:
                    raise AuthenticationError("Authentication failed")
                elif e.response.status_code == 429:
                    raise NetworkError("Rate limit exceeded by server")
                raise NetworkError(f"HTTP error {e.response.status_code}: {e}")
            except httpx.RequestError as e:
                self.logger.error(f"Request error: {e}")
                raise NetworkError(f"Request error: {e}")

    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make a POST request to the API.

        Args:
            endpoint: API endpoint path
            data: Request body data

        Returns:
            Response data
        """
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(include_auth=True)

        # Apply rate limiting if configured
        if self.rate_limiter:
            if not self.rate_limiter.acquire(timeout=30.0):
                raise NetworkError("Rate limit exceeded")

        self.logger.debug(f"POST {url} with data keys: {list(data.keys()) if data else None}")

        with PerformanceContext(f"POST {endpoint}"):
            try:
                response = self.http_client.post(url, json=data, headers=headers)
                self.logger.debug(f"Response status: {response.status_code}")
                response.raise_for_status()

                result = response.json()
                self.logger.debug(f"Response data keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                return result

            except httpx.HTTPStatusError as e:
                self.logger.error(f"HTTP error {e.response.status_code}: {e}")
                if e.response.status_code == 401:
                    raise AuthenticationError("Authentication failed")
                elif e.response.status_code == 429:
                    raise NetworkError("Rate limit exceeded by server")
                raise NetworkError(f"HTTP error {e.response.status_code}: {e}")
            except httpx.RequestError as e:
                self.logger.error(f"Request error: {e}")
                raise NetworkError(f"Request error: {e}")

    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            self.http_client.close()
        if self._websocket_client:
            self._websocket_client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Exit context manager and close client."""
        self.close()


class TradovateAsyncClient(BaseClient):
    """
    Asynchronous Tradovate API client.
    
    This client provides an asynchronous interface to all Tradovate API endpoints.
    """
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)

        # HTTP client will be initialized in async context
        self.http_client: Optional[httpx.AsyncClient] = None

        # Initialize all API modules using shared method
        self._initialize_api_modules()
    
    async def _ensure_http_client(self) -> None:
        """Ensure HTTP client is initialized."""
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(
                timeout=self.timeout,
                headers=self._get_headers(include_auth=False),
                limits=httpx.Limits(
                    max_keepalive_connections=20,
                    max_connections=100,
                    keepalive_expiry=30.0
                ),
                http2=True  # Enable HTTP/2 for better performance
            )
    
    async def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Tradovate API.
        
        Args:
            username: Your Tradovate username
            password: Your Tradovate password
            
        Returns:
            Authentication response data
            
        Raises:
            AuthenticationError: If authentication fails
        """
        await self._ensure_http_client()
        return await self.auth.authenticate(username, password)
    
    async def renew_token(self) -> Dict[str, Any]:
        """
        Renew the current access token.

        Returns:
            Token renewal response data

        Raises:
            AuthenticationError: If token renewal fails
        """
        await self._ensure_http_client()
        return await self.auth.renew_token()

    async def get_async(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make an async GET request to the API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            Response data
        """
        await self._ensure_http_client()
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(include_auth=True)

        # Apply rate limiting if configured
        if self.rate_limiter:
            if not await self.rate_limiter.acquire_async(timeout=30.0):
                raise NetworkError("Rate limit exceeded")

        self.logger.debug(f"GET {url} with params: {params}")

        try:
            response = await self.http_client.get(url, params=params, headers=headers)
            self.logger.debug(f"Response status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            self.logger.debug(f"Response data keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return result

        except httpx.HTTPStatusError as e:
            self.logger.error(f"HTTP error {e.response.status_code}: {e}")
            if e.response.status_code == 401:
                raise AuthenticationError("Authentication failed")
            elif e.response.status_code == 429:
                raise NetworkError("Rate limit exceeded by server")
            raise NetworkError(f"HTTP error {e.response.status_code}: {e}")
        except httpx.RequestError as e:
            self.logger.error(f"Request error: {e}")
            raise NetworkError(f"Request error: {e}")

    async def post_async(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make an async POST request to the API.

        Args:
            endpoint: API endpoint path
            data: Request body data

        Returns:
            Response data
        """
        await self._ensure_http_client()
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(include_auth=True)

        # Apply rate limiting if configured
        if self.rate_limiter:
            if not await self.rate_limiter.acquire_async(timeout=30.0):
                raise NetworkError("Rate limit exceeded")

        self.logger.debug(f"POST {url} with data keys: {list(data.keys()) if data else None}")

        try:
            response = await self.http_client.post(url, json=data, headers=headers)
            self.logger.debug(f"Response status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            self.logger.debug(f"Response data keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return result

        except httpx.HTTPStatusError as e:
            self.logger.error(f"HTTP error {e.response.status_code}: {e}")
            if e.response.status_code == 401:
                raise AuthenticationError("Authentication failed")
            elif e.response.status_code == 429:
                raise NetworkError("Rate limit exceeded by server")
            raise NetworkError(f"HTTP error {e.response.status_code}: {e}")
        except httpx.RequestError as e:
            self.logger.error(f"Request error: {e}")
            raise NetworkError(f"Request error: {e}")

    @property
    def websocket(self) -> WebSocketClient:
        """Get the WebSocket client (creates if not exists)."""
        if self._websocket_client is None:
            self._websocket_client = WebSocketClient(self)
        return self._websocket_client
    
    async def close(self) -> None:
        """Close the HTTP client and clean up resources."""
        if self.http_client:
            await self.http_client.aclose()
        if self._websocket_client:
            await self._websocket_client.close()
    
    async def __aenter__(self):
        await self._ensure_http_client()
        return self
    
    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        """Exit async context manager and close client."""
        await self.close()
