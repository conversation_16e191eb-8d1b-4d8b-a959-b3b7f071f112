"""
Configuration management for the Tradovate SDK.

This module provides configuration loading and management functionality.
"""

import os
import json
from typing import Optional, Dict, Any, Union
from pathlib import Path
from dataclasses import dataclass, field

from ..enums import Environment, LogLevel


@dataclass
class Config:
    """
    Configuration class for the Tradovate SDK.

    This class contains all configuration options for the SDK with their
    possible values and usage scenarios documented.
    """

    # API Configuration
    api_key: Optional[str] = None  # Your Tradovate API key (required for authentication)
    api_secret: Optional[str] = None  # Your Tradovate API secret (required for authentication)

    # Environment: "demo" for testing, "live" for production trading
    # Values: Environment.DEMO, Environment.LIVE
    # Default: Environment.DEMO (always start with demo for safety)
    environment: Environment = Environment.DEMO

    # Application identifier for API requests
    # Default: "TradovateSDK" (can be customized for your application)
    app_id: str = "TradovateSDK"

    # Application version for API requests
    # Default: "1.0.0" (can be customized for your application)
    app_version: str = "1.0.0"

    # Network Configuration
    # Request timeout in seconds (how long to wait for API responses)
    # Range: 1.0 - 300.0 seconds
    # Default: 30.0 (good for most use cases)
    timeout: float = 30.0

    # Maximum number of retry attempts for failed requests
    # Range: 0 - 10 retries
    # Default: 3 (balances reliability with performance)
    max_retries: int = 3

    # Delay between retry attempts in seconds
    # Range: 0.1 - 60.0 seconds
    # Default: 1.0 (exponential backoff is applied)
    retry_delay: float = 1.0

    # Rate limiting: maximum requests per second (None = no limit)
    # Range: 1 - 100 requests/second or None
    # Default: None (respects Tradovate's built-in limits)
    # Use 10-20 for conservative rate limiting
    rate_limit: Optional[int] = None

    # WebSocket Configuration
    # WebSocket connection timeout in seconds
    # Range: 5.0 - 120.0 seconds
    # Default: 30.0 (good for real-time data)
    websocket_timeout: float = 30.0

    # Interval between ping messages to keep connection alive (seconds)
    # Range: 10.0 - 60.0 seconds
    # Default: 20.0 (prevents connection timeouts)
    websocket_ping_interval: float = 20.0

    # Timeout for ping response (seconds)
    # Range: 5.0 - 30.0 seconds
    # Default: 10.0 (detects connection issues quickly)
    websocket_ping_timeout: float = 10.0

    # Automatically reconnect on connection loss
    # Values: True (recommended), False
    # Default: True (ensures continuous data flow)
    websocket_auto_reconnect: bool = True

    # Maximum number of reconnection attempts
    # Range: 1 - 20 attempts
    # Default: 5 (balances persistence with resource usage)
    websocket_max_reconnect_attempts: int = 5

    # Logging Configuration
    # Log level for SDK messages
    # Values: LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARNING, LogLevel.ERROR, LogLevel.CRITICAL
    # Default: LogLevel.INFO (good for production)
    # Use DEBUG for development, WARNING+ for production
    log_level: LogLevel = LogLevel.INFO

    # Log message format string
    # Default: Standard format with timestamp, logger name, level, and message
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Log file path (None = console only)
    # Values: None (console), "/path/to/logfile.log" (file)
    # Default: None (logs to console)
    log_file: Optional[str] = None

    # Cache Configuration
    # Enable response caching for improved performance
    # Values: True (recommended), False
    # Default: True (reduces API calls and improves speed)
    enable_cache: bool = True

    # Cache time-to-live in seconds
    # Range: 60 - 3600 seconds (1 minute to 1 hour)
    # Default: 300 (5 minutes, good for most data)
    cache_ttl: int = 300

    # Maximum number of cached items
    # Range: 100 - 10000 items
    # Default: 1000 (balances memory usage with performance)
    cache_max_size: int = 1000
    
    # Additional settings
    extra_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization processing."""
        # Convert string environment to enum
        if isinstance(self.environment, str):
            self.environment = Environment(self.environment)
        
        # Convert string log level to enum
        if isinstance(self.log_level, str):
            self.log_level = LogLevel(self.log_level)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Config":
        """Create config from dictionary."""
        # Extract known fields
        known_fields = {
            field.name for field in cls.__dataclass_fields__.values()
        }
        
        config_data = {}
        extra_settings = {}
        
        for key, value in data.items():
            if key in known_fields:
                config_data[key] = value
            else:
                extra_settings[key] = value
        
        if extra_settings:
            config_data["extra_settings"] = extra_settings
        
        return cls(**config_data)
    
    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> "Config":
        """Load configuration from file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        with open(file_path, 'r') as f:
            if file_path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                # Assume it's a simple key=value format
                data = {}
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        data[key.strip()] = value.strip()
        
        return cls.from_dict(data)
    
    @classmethod
    def from_env(cls, prefix: str = "TRADOVATE_") -> "Config":
        """Load configuration from environment variables."""
        data = {}
        
        # Map of config field names to environment variable names
        env_mapping = {
            "api_key": f"{prefix}API_KEY",
            "api_secret": f"{prefix}API_SECRET",
            "environment": f"{prefix}ENVIRONMENT",
            "app_id": f"{prefix}APP_ID",
            "app_version": f"{prefix}APP_VERSION",
            "timeout": f"{prefix}TIMEOUT",
            "max_retries": f"{prefix}MAX_RETRIES",
            "retry_delay": f"{prefix}RETRY_DELAY",
            "rate_limit": f"{prefix}RATE_LIMIT",
            "websocket_timeout": f"{prefix}WEBSOCKET_TIMEOUT",
            "websocket_ping_interval": f"{prefix}WEBSOCKET_PING_INTERVAL",
            "websocket_ping_timeout": f"{prefix}WEBSOCKET_PING_TIMEOUT",
            "websocket_auto_reconnect": f"{prefix}WEBSOCKET_AUTO_RECONNECT",
            "websocket_max_reconnect_attempts": f"{prefix}WEBSOCKET_MAX_RECONNECT_ATTEMPTS",
            "log_level": f"{prefix}LOG_LEVEL",
            "log_format": f"{prefix}LOG_FORMAT",
            "log_file": f"{prefix}LOG_FILE",
            "enable_cache": f"{prefix}ENABLE_CACHE",
            "cache_ttl": f"{prefix}CACHE_TTL",
            "cache_max_size": f"{prefix}CACHE_MAX_SIZE",
        }
        
        for field_name, env_var in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert string values to appropriate types
                field_type = cls.__dataclass_fields__[field_name].type
                
                if field_type == bool:
                    data[field_name] = value.lower() in ('true', '1', 'yes', 'on')
                elif field_type == int:
                    data[field_name] = int(value)
                elif field_type == float:
                    data[field_name] = float(value)
                else:
                    data[field_name] = value
        
        return cls.from_dict(data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        result = {}
        
        for field_name in self.__dataclass_fields__.keys():
            value = getattr(self, field_name)
            
            # Convert enums to their values
            if hasattr(value, 'value'):
                value = value.value
            
            result[field_name] = value
        
        return result
    
    def update(self, **kwargs) -> None:
        """Update configuration with new values."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.extra_settings[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        if hasattr(self, key):
            return getattr(self, key)
        return self.extra_settings.get(key, default)


def load_config(
    config_file: Optional[Union[str, Path]] = None,
    env_prefix: str = "TRADOVATE_",
    **kwargs
) -> Config:
    """
    Load configuration from multiple sources.
    
    Priority order:
    1. Keyword arguments (highest priority)
    2. Environment variables
    3. Configuration file
    4. Default values (lowest priority)
    
    Args:
        config_file: Path to configuration file
        env_prefix: Prefix for environment variables
        **kwargs: Direct configuration values
    
    Returns:
        Loaded configuration
    """
    # Start with default config
    config = Config()
    
    # Load from file if provided
    if config_file:
        try:
            file_config = Config.from_file(config_file)
            config.update(**file_config.to_dict())
        except FileNotFoundError:
            pass  # File doesn't exist, use defaults
    
    # Load from environment variables
    env_config = Config.from_env(env_prefix)
    config.update(**env_config.to_dict())
    
    # Apply direct keyword arguments
    config.update(**kwargs)
    
    return config


# Default configuration paths to check
DEFAULT_CONFIG_PATHS = [
    Path.cwd() / "tradovate.json",
    Path.cwd() / "tradovate.conf",
    Path.home() / ".tradovate" / "config.json",
    Path.home() / ".tradovate" / "config.conf",
]


def find_config_file() -> Optional[Path]:
    """Find configuration file in default locations."""
    for path in DEFAULT_CONFIG_PATHS:
        if path.exists():
            return path
    return None
