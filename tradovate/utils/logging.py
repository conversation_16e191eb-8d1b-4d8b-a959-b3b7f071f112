"""
Logging utilities for the Tradovate SDK.

This module provides logging configuration and utilities.
"""

import logging
import sys
from typing import Optional, Dict, Any
from pathlib import Path

from ..enums import LogLevel


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(f"tradovate.{name}")


def setup_logging(
    level: LogLevel = LogLevel.INFO,
    format_string: Optional[str] = None,
    log_file: Optional[str] = None,
    console: bool = True,
    **kwargs
) -> None:
    """
    Set up logging configuration for the Tradovate SDK.
    
    Args:
        level: Logging level
        format_string: Custom format string
        log_file: Log file path
        console: Whether to log to console
        **kwargs: Additional logging configuration
    """
    # Default format
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Convert LogLevel enum to logging level
    log_level = getattr(logging, level.value)
    
    # Configure root logger
    root_logger = logging.getLogger("tradovate")
    root_logger.setLevel(log_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    
    # Console handler
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Prevent propagation to avoid duplicate logs
    root_logger.propagate = False


def configure_request_logging(enabled: bool = False) -> None:
    """
    Configure HTTP request logging.
    
    Args:
        enabled: Whether to enable request logging
    """
    if enabled:
        # Enable httpx logging
        logging.getLogger("httpx").setLevel(logging.DEBUG)
    else:
        # Disable httpx logging
        logging.getLogger("httpx").setLevel(logging.WARNING)


class StructuredLogger:
    """Structured logger for consistent log formatting."""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
    
    def _log_structured(
        self, 
        level: int, 
        message: str, 
        **kwargs
    ) -> None:
        """Log with structured data."""
        if kwargs:
            extra_data = " | ".join(f"{k}={v}" for k, v in kwargs.items())
            full_message = f"{message} | {extra_data}"
        else:
            full_message = message
        
        self.logger.log(level, full_message)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self._log_structured(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self._log_structured(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self._log_structured(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self._log_structured(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self._log_structured(logging.CRITICAL, message, **kwargs)


class APILogger:
    """Specialized logger for API operations."""
    
    def __init__(self, name: str):
        self.logger = get_logger(f"api.{name}")
    
    def log_request(
        self, 
        method: str, 
        url: str, 
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log API request."""
        self.logger.debug(
            f"API Request: {method} {url}",
            extra={
                "method": method,
                "url": url,
                "headers": headers,
                "data": data,
            }
        )
    
    def log_response(
        self, 
        status_code: int, 
        response_time: float,
        response_size: Optional[int] = None
    ) -> None:
        """Log API response."""
        self.logger.debug(
            f"API Response: {status_code} ({response_time:.3f}s)",
            extra={
                "status_code": status_code,
                "response_time": response_time,
                "response_size": response_size,
            }
        )
    
    def log_error(
        self, 
        error: Exception, 
        method: str, 
        url: str
    ) -> None:
        """Log API error."""
        self.logger.error(
            f"API Error: {method} {url} - {error}",
            extra={
                "method": method,
                "url": url,
                "error_type": type(error).__name__,
                "error_message": str(error),
            }
        )


class WebSocketLogger:
    """Specialized logger for WebSocket operations."""
    
    def __init__(self, name: str):
        self.logger = get_logger(f"websocket.{name}")
    
    def log_connection(self, url: str) -> None:
        """Log WebSocket connection."""
        self.logger.info(f"WebSocket connecting to: {url}")
    
    def log_connected(self, url: str) -> None:
        """Log successful WebSocket connection."""
        self.logger.info(f"WebSocket connected to: {url}")
    
    def log_disconnection(self, url: str, reason: Optional[str] = None) -> None:
        """Log WebSocket disconnection."""
        message = f"WebSocket disconnected from: {url}"
        if reason:
            message += f" - {reason}"
        self.logger.info(message)
    
    def log_message_sent(self, message_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """Log WebSocket message sent."""
        self.logger.debug(f"WebSocket message sent: {message_type}", extra={"data": data})
    
    def log_message_received(self, message_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """Log WebSocket message received."""
        self.logger.debug(f"WebSocket message received: {message_type}", extra={"data": data})
    
    def log_error(self, error: Exception) -> None:
        """Log WebSocket error."""
        self.logger.error(
            f"WebSocket error: {error}",
            extra={
                "error_type": type(error).__name__,
                "error_message": str(error),
            }
        )
