"""
Rate limiting functionality for the Tradovate SDK.

This module provides rate limiting to ensure API usage stays within limits.
"""

import asyncio
import time
from typing import Optional, Dict, Any
from collections import deque
from threading import Lock


class RateLimiter:
    """
    Rate limiter implementation using token bucket algorithm.
    
    This class provides both synchronous and asynchronous rate limiting
    to control the rate of API requests.
    """
    
    def __init__(
        self,
        max_requests: int,
        time_window: float = 1.0,
        burst_size: Optional[int] = None
    ) -> None:
        """
        Initialize the rate limiter.
        
        Args:
            max_requests: Maximum number of requests allowed per time window
            time_window: Time window in seconds (default: 1 second)
            burst_size: Maximum burst size (default: same as max_requests)
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.burst_size = burst_size or max_requests
        
        # Token bucket implementation
        self.tokens = float(self.burst_size)
        self.last_update = time.time()
        self.lock = Lock()
        
        # Request tracking for sliding window
        self.requests = deque()
        
        # Rate limiting statistics
        self.total_requests = 0
        self.blocked_requests = 0
        self.last_block_time: Optional[float] = None
    
    def _update_tokens(self) -> None:
        """Update available tokens based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_update
        
        # Add tokens based on elapsed time
        tokens_to_add = elapsed * (self.max_requests / self.time_window)
        self.tokens = min(self.burst_size, self.tokens + tokens_to_add)
        self.last_update = now
    
    def _can_proceed(self) -> bool:
        """Check if a request can proceed."""
        with self.lock:
            self._update_tokens()
            
            if self.tokens >= 1.0:
                self.tokens -= 1.0
                self.total_requests += 1
                return True
            else:
                self.blocked_requests += 1
                self.last_block_time = time.time()
                return False
    
    def _time_until_next_token(self) -> float:
        """Calculate time until next token is available."""
        with self.lock:
            self._update_tokens()
            
            if self.tokens >= 1.0:
                return 0.0
            
            # Calculate time needed for one token
            tokens_needed = 1.0 - self.tokens
            time_per_token = self.time_window / self.max_requests
            return tokens_needed * time_per_token
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """
        Acquire permission to make a request (synchronous).
        
        Args:
            timeout: Maximum time to wait for permission (None for no timeout)
            
        Returns:
            True if permission granted, False if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            if self._can_proceed():
                return True
            
            # Check timeout
            if timeout is not None:
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    return False
            
            # Wait for next token
            wait_time = min(self._time_until_next_token(), 0.1)
            time.sleep(wait_time)
    
    async def acquire_async(self, timeout: Optional[float] = None) -> bool:
        """
        Acquire permission to make a request (asynchronous).
        
        Args:
            timeout: Maximum time to wait for permission (None for no timeout)
            
        Returns:
            True if permission granted, False if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            if self._can_proceed():
                return True
            
            # Check timeout
            if timeout is not None:
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    return False
            
            # Wait for next token
            wait_time = min(self._time_until_next_token(), 0.1)
            await asyncio.sleep(wait_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        with self.lock:
            self._update_tokens()
            
            return {
                "max_requests": self.max_requests,
                "time_window": self.time_window,
                "burst_size": self.burst_size,
                "available_tokens": self.tokens,
                "total_requests": self.total_requests,
                "blocked_requests": self.blocked_requests,
                "block_rate": (
                    self.blocked_requests / max(self.total_requests, 1)
                    if self.total_requests > 0 else 0.0
                ),
                "last_block_time": self.last_block_time,
                "time_until_next_token": self._time_until_next_token(),
            }
    
    def reset(self) -> None:
        """Reset the rate limiter state."""
        with self.lock:
            self.tokens = float(self.burst_size)
            self.last_update = time.time()
            self.requests.clear()
            self.total_requests = 0
            self.blocked_requests = 0
            self.last_block_time = None
    
    def __enter__(self):
        """Context manager entry."""
        self.acquire()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        pass


class SlidingWindowRateLimiter:
    """
    Rate limiter using sliding window algorithm.
    
    This implementation tracks actual request timestamps for more precise
    rate limiting but uses more memory.
    """
    
    def __init__(
        self,
        max_requests: int,
        time_window: float = 1.0
    ) -> None:
        """
        Initialize the sliding window rate limiter.
        
        Args:
            max_requests: Maximum number of requests allowed per time window
            time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()
        self.lock = Lock()
        
        # Statistics
        self.total_requests = 0
        self.blocked_requests = 0
    
    def _cleanup_old_requests(self) -> None:
        """Remove requests outside the time window."""
        now = time.time()
        cutoff = now - self.time_window
        
        while self.requests and self.requests[0] < cutoff:
            self.requests.popleft()
    
    def _can_proceed(self) -> bool:
        """Check if a request can proceed."""
        with self.lock:
            self._cleanup_old_requests()
            
            if len(self.requests) < self.max_requests:
                self.requests.append(time.time())
                self.total_requests += 1
                return True
            else:
                self.blocked_requests += 1
                return False
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """
        Acquire permission to make a request (synchronous).
        
        Args:
            timeout: Maximum time to wait for permission
            
        Returns:
            True if permission granted, False if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            if self._can_proceed():
                return True
            
            # Check timeout
            if timeout is not None:
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    return False
            
            # Wait a bit before trying again
            time.sleep(0.01)
    
    async def acquire_async(self, timeout: Optional[float] = None) -> bool:
        """
        Acquire permission to make a request (asynchronous).
        
        Args:
            timeout: Maximum time to wait for permission
            
        Returns:
            True if permission granted, False if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            if self._can_proceed():
                return True
            
            # Check timeout
            if timeout is not None:
                elapsed = time.time() - start_time
                if elapsed >= timeout:
                    return False
            
            # Wait a bit before trying again
            await asyncio.sleep(0.01)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        with self.lock:
            self._cleanup_old_requests()
            
            return {
                "max_requests": self.max_requests,
                "time_window": self.time_window,
                "current_requests": len(self.requests),
                "total_requests": self.total_requests,
                "blocked_requests": self.blocked_requests,
                "block_rate": (
                    self.blocked_requests / max(self.total_requests, 1)
                    if self.total_requests > 0 else 0.0
                ),
            }
