"""
Simple caching utilities for the Tradovate SDK.

This module provides basic caching functionality to improve performance
by reducing redundant API calls for frequently accessed data.
"""

import time
import asyncio
from typing import Any, Dict, Optional, Callable, Union
from threading import Lock
from functools import wraps


class CacheEntry:
    """A single cache entry with expiration."""
    
    def __init__(self, value: Any, ttl: float) -> None:
        """
        Initialize a cache entry.
        
        Args:
            value: The cached value
            ttl: Time to live in seconds
        """
        self.value = value
        self.expires_at = time.time() + ttl
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        return time.time() > self.expires_at


class SimpleCache:
    """A simple in-memory cache with TTL support."""
    
    def __init__(self, default_ttl: float = 300.0, max_size: int = 1000) -> None:
        """
        Initialize the cache.
        
        Args:
            default_ttl: Default time to live in seconds (5 minutes)
            max_size: Maximum number of entries to store
        """
        self.default_ttl = default_ttl
        self.max_size = max_size
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = Lock()
        
        # Statistics
        self.hits = 0
        self.misses = 0
        self.evictions = 0
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                self.misses += 1
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self.misses += 1
                return None
            
            self.hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if None)
        """
        if ttl is None:
            ttl = self.default_ttl
        
        with self._lock:
            # Evict expired entries if we're at capacity
            if len(self._cache) >= self.max_size:
                self._evict_expired()
                
                # If still at capacity, evict oldest entry
                if len(self._cache) >= self.max_size:
                    oldest_key = min(self._cache.keys(), 
                                   key=lambda k: self._cache[k].expires_at)
                    del self._cache[oldest_key]
                    self.evictions += 1
            
            self._cache[key] = CacheEntry(value, ttl)
    
    def delete(self, key: str) -> bool:
        """
        Delete a key from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key was deleted, False if not found
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all entries from the cache."""
        with self._lock:
            self._cache.clear()
            self.hits = 0
            self.misses = 0
            self.evictions = 0
    
    def _evict_expired(self) -> None:
        """Remove expired entries from the cache."""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry.expires_at <= current_time
        ]
        for key in expired_keys:
            del self._cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / total_requests if total_requests > 0 else 0.0
            
            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "hits": self.hits,
                "misses": self.misses,
                "evictions": self.evictions,
                "hit_rate": hit_rate,
                "total_requests": total_requests
            }


def cached(ttl: float = 300.0, key_func: Optional[Callable] = None):
    """
    Decorator to cache function results.
    
    Args:
        ttl: Time to live in seconds
        key_func: Function to generate cache key from args/kwargs
    """
    def decorator(func):
        cache = SimpleCache(default_ttl=ttl)
        
        def default_key_func(*args, **kwargs):
            """Default key generation function."""
            return f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
        
        key_generator = key_func or default_key_func
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = key_generator(*args, **kwargs)
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Call function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache_key = key_generator(*args, **kwargs)
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Call function and cache result
            result = await func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            async_wrapper.cache = cache
            return async_wrapper
        else:
            wrapper.cache = cache
            return wrapper
    
    return decorator


# Global cache instance for the SDK
_global_cache = SimpleCache(default_ttl=300.0, max_size=1000)


def get_global_cache() -> SimpleCache:
    """Get the global cache instance."""
    return _global_cache


def clear_global_cache() -> None:
    """Clear the global cache."""
    _global_cache.clear()


def get_cache_stats() -> Dict[str, Any]:
    """Get global cache statistics."""
    return _global_cache.get_stats()
