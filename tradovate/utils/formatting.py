"""
Formatting utilities for the Tradovate SDK.

This module provides functions for formatting various data types
commonly used in trading applications.
"""

from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Union


def format_price(
    price: Union[float, int, Decimal, None], 
    precision: int = 2,
    currency_symbol: str = "$"
) -> str:
    """
    Format price value for display.
    
    Args:
        price: Price value to format
        precision: Number of decimal places (default: 2)
        currency_symbol: Currency symbol to prepend (default: "$")
        
    Returns:
        Formatted price string
        
    Examples:
        >>> format_price(1234.567)
        '$1,234.57'
        >>> format_price(1234.567, precision=3)
        '$1,234.567'
        >>> format_price(None)
        'N/A'
    """
    if price is None:
        return "N/A"
    
    try:
        # Convert to Decimal for precise formatting
        if isinstance(price, Decimal):
            decimal_price = price
        else:
            decimal_price = Decimal(str(price))
        
        # Round to specified precision
        rounded_price = decimal_price.quantize(
            Decimal('0.' + '0' * precision), 
            rounding=ROUND_HALF_UP
        )
        
        # Format with thousands separator
        formatted = f"{rounded_price:,.{precision}f}"
        
        return f"{currency_symbol}{formatted}"
    
    except (ValueError, TypeError, ArithmeticError):
        return "Invalid"


def format_quantity(
    quantity: Union[int, float, None],
    precision: int = 0
) -> str:
    """
    Format quantity value for display.
    
    Args:
        quantity: Quantity value to format
        precision: Number of decimal places (default: 0)
        
    Returns:
        Formatted quantity string
        
    Examples:
        >>> format_quantity(1000)
        '1,000'
        >>> format_quantity(1000.5, precision=1)
        '1,000.5'
        >>> format_quantity(None)
        'N/A'
    """
    if quantity is None:
        return "N/A"
    
    try:
        if precision == 0:
            return f"{int(quantity):,}"
        else:
            return f"{float(quantity):,.{precision}f}"
    except (ValueError, TypeError):
        return "Invalid"


def format_timestamp(
    timestamp: Union[datetime, str, int, float, None],
    format_string: str = "%Y-%m-%d %H:%M:%S UTC",
    timezone_aware: bool = True
) -> str:
    """
    Format timestamp for display.
    
    Args:
        timestamp: Timestamp to format (datetime, ISO string, or Unix timestamp)
        format_string: Format string for output (default: "%Y-%m-%d %H:%M:%S UTC")
        timezone_aware: Whether to ensure timezone awareness (default: True)
        
    Returns:
        Formatted timestamp string
        
    Examples:
        >>> format_timestamp(datetime.now())
        '2024-01-15 14:30:45 UTC'
        >>> format_timestamp("2024-01-15T14:30:45Z")
        '2024-01-15 14:30:45 UTC'
        >>> format_timestamp(None)
        'N/A'
    """
    if timestamp is None:
        return "N/A"
    
    try:
        # Convert to datetime if needed
        if isinstance(timestamp, datetime):
            dt = timestamp
        elif isinstance(timestamp, str):
            # Try to parse ISO format
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        elif isinstance(timestamp, (int, float)):
            # Unix timestamp
            dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        else:
            return "Invalid"
        
        # Ensure timezone awareness if requested
        if timezone_aware and dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        return dt.strftime(format_string)
    
    except (ValueError, TypeError, OSError):
        return "Invalid"


def format_percentage(
    value: Union[float, int, None],
    precision: int = 2,
    include_sign: bool = True
) -> str:
    """
    Format percentage value for display.
    
    Args:
        value: Percentage value (as decimal, e.g., 0.05 for 5%)
        precision: Number of decimal places (default: 2)
        include_sign: Whether to include + sign for positive values (default: True)
        
    Returns:
        Formatted percentage string
        
    Examples:
        >>> format_percentage(0.0567)
        '+5.67%'
        >>> format_percentage(-0.0234)
        '-2.34%'
        >>> format_percentage(None)
        'N/A'
    """
    if value is None:
        return "N/A"
    
    try:
        percentage = float(value) * 100
        sign = "+" if include_sign and percentage > 0 else ""
        return f"{sign}{percentage:.{precision}f}%"
    except (ValueError, TypeError):
        return "Invalid"


def format_currency(
    amount: Union[float, int, Decimal, None],
    currency_code: str = "USD",
    precision: int = 2
) -> str:
    """
    Format currency amount with currency code.
    
    Args:
        amount: Currency amount to format
        currency_code: Currency code (default: "USD")
        precision: Number of decimal places (default: 2)
        
    Returns:
        Formatted currency string
        
    Examples:
        >>> format_currency(1234.56)
        '1,234.56 USD'
        >>> format_currency(1234.56, "EUR")
        '1,234.56 EUR'
        >>> format_currency(None)
        'N/A'
    """
    if amount is None:
        return "N/A"
    
    try:
        formatted_amount = f"{float(amount):,.{precision}f}"
        return f"{formatted_amount} {currency_code}"
    except (ValueError, TypeError):
        return "Invalid"


def format_pnl(
    pnl: Union[float, int, None],
    precision: int = 2,
    currency_symbol: str = "$"
) -> str:
    """
    Format profit/loss value with appropriate coloring indicators.
    
    Args:
        pnl: P&L value to format
        precision: Number of decimal places (default: 2)
        currency_symbol: Currency symbol to prepend (default: "$")
        
    Returns:
        Formatted P&L string with sign
        
    Examples:
        >>> format_pnl(123.45)
        '+$123.45'
        >>> format_pnl(-67.89)
        '-$67.89'
        >>> format_pnl(None)
        'N/A'
    """
    if pnl is None:
        return "N/A"
    
    try:
        value = float(pnl)
        sign = "+" if value > 0 else ""
        formatted = f"{value:,.{precision}f}"
        return f"{sign}{currency_symbol}{formatted}"
    except (ValueError, TypeError):
        return "Invalid"


def truncate_string(
    text: Optional[str],
    max_length: int = 50,
    suffix: str = "..."
) -> str:
    """
    Truncate string to specified length with suffix.
    
    Args:
        text: Text to truncate
        max_length: Maximum length (default: 50)
        suffix: Suffix to add when truncated (default: "...")
        
    Returns:
        Truncated string
        
    Examples:
        >>> truncate_string("This is a very long string", 10)
        'This is...'
        >>> truncate_string("Short", 10)
        'Short'
        >>> truncate_string(None)
        'N/A'
    """
    if text is None:
        return "N/A"
    
    text_str = str(text)
    if len(text_str) <= max_length:
        return text_str
    
    return text_str[:max_length - len(suffix)] + suffix
