"""
Validation utilities for the Tradovate SDK.

This module provides input validation functions for API parameters.
"""

import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from ..exceptions import ValidationError
from ..enums import OrderAction, OrderType, TimeInForce


def validate_account_id(account_id: Any) -> int:
    """
    Validate account ID.
    
    Args:
        account_id: Account ID to validate
        
    Returns:
        Validated account ID
        
    Raises:
        ValidationError: If account ID is invalid
    """
    if account_id is None:
        raise ValidationError("Account ID is required")
    
    try:
        account_id = int(account_id)
        if account_id <= 0:
            raise ValidationError("Account ID must be positive")
        return account_id
    except (ValueError, TypeError):
        raise ValidationError("Account ID must be a valid integer")


def validate_contract_id(contract_id: Any) -> int:
    """
    Validate contract ID.
    
    Args:
        contract_id: Contract ID to validate
        
    Returns:
        Validated contract ID
        
    Raises:
        ValidationError: If contract ID is invalid
    """
    if contract_id is None:
        raise ValidationError("Contract ID is required")
    
    try:
        contract_id = int(contract_id)
        if contract_id <= 0:
            raise ValidationError("Contract ID must be positive")
        return contract_id
    except (ValueError, TypeError):
        raise ValidationError("Contract ID must be a valid integer")


def validate_quantity(qty: Any) -> int:
    """
    Validate order quantity.
    
    Args:
        qty: Quantity to validate
        
    Returns:
        Validated quantity
        
    Raises:
        ValidationError: If quantity is invalid
    """
    if qty is None:
        raise ValidationError("Quantity is required")
    
    try:
        qty = int(qty)
        if qty <= 0:
            raise ValidationError("Quantity must be positive")
        if qty > 10000:  # Reasonable upper limit
            raise ValidationError("Quantity exceeds maximum allowed")
        return qty
    except (ValueError, TypeError):
        raise ValidationError("Quantity must be a valid integer")


def validate_price(price: Any, allow_none: bool = True) -> Optional[float]:
    """
    Validate price value.
    
    Args:
        price: Price to validate
        allow_none: Whether None is allowed
        
    Returns:
        Validated price
        
    Raises:
        ValidationError: If price is invalid
    """
    if price is None:
        if allow_none:
            return None
        raise ValidationError("Price is required")
    
    try:
        price = float(price)
        if price <= 0:
            raise ValidationError("Price must be positive")
        if price > 1000000:  # Reasonable upper limit
            raise ValidationError("Price exceeds maximum allowed")
        return price
    except (ValueError, TypeError):
        raise ValidationError("Price must be a valid number")


def validate_order_action(action: Any) -> str:
    """
    Validate order action.
    
    Args:
        action: Order action to validate
        
    Returns:
        Validated order action
        
    Raises:
        ValidationError: If action is invalid
    """
    if action is None:
        raise ValidationError("Order action is required")
    
    if isinstance(action, OrderAction):
        return action.value
    
    action_str = str(action).strip()
    valid_actions = [e.value for e in OrderAction]
    
    if action_str not in valid_actions:
        raise ValidationError(f"Invalid order action. Must be one of: {valid_actions}")
    
    return action_str


def validate_order_type(order_type: Any) -> str:
    """
    Validate order type.
    
    Args:
        order_type: Order type to validate
        
    Returns:
        Validated order type
        
    Raises:
        ValidationError: If order type is invalid
    """
    if order_type is None:
        raise ValidationError("Order type is required")
    
    if isinstance(order_type, OrderType):
        return order_type.value
    
    type_str = str(order_type).strip()
    valid_types = [e.value for e in OrderType]
    
    if type_str not in valid_types:
        raise ValidationError(f"Invalid order type. Must be one of: {valid_types}")
    
    return type_str


def validate_time_in_force(tif: Any) -> str:
    """
    Validate time in force.
    
    Args:
        tif: Time in force to validate
        
    Returns:
        Validated time in force
        
    Raises:
        ValidationError: If time in force is invalid
    """
    if tif is None:
        raise ValidationError("Time in force is required")
    
    if isinstance(tif, TimeInForce):
        return tif.value
    
    tif_str = str(tif).strip()
    valid_tifs = [e.value for e in TimeInForce]
    
    if tif_str not in valid_tifs:
        raise ValidationError(f"Invalid time in force. Must be one of: {valid_tifs}")
    
    return tif_str


def validate_email(email: Any) -> str:
    """
    Validate email address.
    
    Args:
        email: Email to validate
        
    Returns:
        Validated email
        
    Raises:
        ValidationError: If email is invalid
    """
    if email is None:
        raise ValidationError("Email is required")
    
    email_str = str(email).strip()
    
    if not email_str:
        raise ValidationError("Email cannot be empty")
    
    # Basic email validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email_str):
        raise ValidationError("Invalid email format")
    
    return email_str


def validate_password(password: Any) -> str:
    """
    Validate password.
    
    Args:
        password: Password to validate
        
    Returns:
        Validated password
        
    Raises:
        ValidationError: If password is invalid
    """
    if password is None:
        raise ValidationError("Password is required")
    
    password_str = str(password)
    
    if len(password_str) < 8:
        raise ValidationError("Password must be at least 8 characters long")
    
    if len(password_str) > 128:
        raise ValidationError("Password is too long")
    
    return password_str


def validate_api_key(api_key: Any) -> str:
    """
    Validate API key.
    
    Args:
        api_key: API key to validate
        
    Returns:
        Validated API key
        
    Raises:
        ValidationError: If API key is invalid
    """
    if api_key is None:
        raise ValidationError("API key is required")
    
    key_str = str(api_key).strip()
    
    if not key_str:
        raise ValidationError("API key cannot be empty")
    
    if len(key_str) < 10:
        raise ValidationError("API key appears to be too short")
    
    return key_str


def validate_datetime(dt: Any, allow_none: bool = True) -> Optional[datetime]:
    """
    Validate datetime value.
    
    Args:
        dt: Datetime to validate
        allow_none: Whether None is allowed
        
    Returns:
        Validated datetime
        
    Raises:
        ValidationError: If datetime is invalid
    """
    if dt is None:
        if allow_none:
            return None
        raise ValidationError("Datetime is required")
    
    if isinstance(dt, datetime):
        return dt
    
    if isinstance(dt, str):
        try:
            # Try to parse ISO format
            return datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            pass
        
        try:
            # Try common formats
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y-%m-%dT%H:%M:%S']:
                try:
                    return datetime.strptime(dt, fmt)
                except ValueError:
                    continue
        except ValueError:
            pass
    
    raise ValidationError("Invalid datetime format")


def validate_order(order_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate complete order data.
    
    Args:
        order_data: Order data to validate
        
    Returns:
        Validated order data
        
    Raises:
        ValidationError: If order data is invalid
    """
    validated = {}
    
    # Required fields
    validated['accountId'] = validate_account_id(order_data.get('accountId'))
    validated['contractId'] = validate_contract_id(order_data.get('contractId'))
    validated['action'] = validate_order_action(order_data.get('action'))
    validated['orderType'] = validate_order_type(order_data.get('orderType'))
    validated['qty'] = validate_quantity(order_data.get('qty'))
    
    # Optional fields
    if 'price' in order_data:
        validated['price'] = validate_price(order_data['price'])
    
    if 'stopPrice' in order_data:
        validated['stopPrice'] = validate_price(order_data['stopPrice'])
    
    if 'timeInForce' in order_data:
        validated['timeInForce'] = validate_time_in_force(order_data['timeInForce'])
    
    # Order type specific validation
    order_type = validated['orderType']
    
    if order_type in ['Limit', 'StopLimit', 'LIT'] and 'price' not in validated:
        raise ValidationError(f"{order_type} orders require a price")
    
    if order_type in ['StopMarket', 'StopLimit', 'TrailingStop'] and 'stopPrice' not in validated:
        raise ValidationError(f"{order_type} orders require a stop price")
    
    # Copy other fields as-is
    for key, value in order_data.items():
        if key not in validated and value is not None:
            validated[key] = value
    
    return validated


def validate_pagination_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate pagination parameters.
    
    Args:
        params: Pagination parameters
        
    Returns:
        Validated parameters
        
    Raises:
        ValidationError: If parameters are invalid
    """
    validated = {}
    
    if 'page' in params:
        try:
            page = int(params['page'])
            if page < 1:
                raise ValidationError("Page number must be positive")
            validated['page'] = page
        except (ValueError, TypeError):
            raise ValidationError("Page must be a valid integer")
    
    if 'pageSize' in params:
        try:
            page_size = int(params['pageSize'])
            if page_size < 1:
                raise ValidationError("Page size must be positive")
            if page_size > 1000:
                raise ValidationError("Page size cannot exceed 1000")
            validated['pageSize'] = page_size
        except (ValueError, TypeError):
            raise ValidationError("Page size must be a valid integer")
    
    if 'limit' in params:
        try:
            limit = int(params['limit'])
            if limit < 1:
                raise ValidationError("Limit must be positive")
            if limit > 1000:
                raise ValidationError("Limit cannot exceed 1000")
            validated['limit'] = limit
        except (ValueError, TypeError):
            raise ValidationError("Limit must be a valid integer")
    
    return validated
