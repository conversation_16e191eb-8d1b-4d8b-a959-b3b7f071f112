"""
Retry logic utilities for the Tradovate SDK.

This module provides retry functionality for handling transient errors.
"""

import asyncio
import time
import random
from typing import Callable, Any, Optional, Type, Union, Tuple
from functools import wraps
from dataclasses import dataclass

from ..exceptions import TradovateError, RateLimitError, NetworkError, TimeoutError


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_strategy: str = "exponential"  # "exponential", "linear", "fixed"
    
    # Exception types to retry on
    retry_exceptions: Tuple[Type[Exception], ...] = (
        NetworkError,
        TimeoutError,
        RateLimitError,
    )
    
    # Exception types to never retry
    no_retry_exceptions: Tuple[Type[Exception], ...] = (
        KeyboardInterrupt,
        SystemExit,
    )


def calculate_delay(attempt: int, config: RetryConfig) -> float:
    """
    Calculate delay for retry attempt.
    
    Args:
        attempt: Current attempt number (0-based)
        config: Retry configuration
        
    Returns:
        Delay in seconds
    """
    if config.backoff_strategy == "exponential":
        delay = config.base_delay * (config.exponential_base ** attempt)
    elif config.backoff_strategy == "linear":
        delay = config.base_delay * (attempt + 1)
    else:  # fixed
        delay = config.base_delay
    
    # Apply maximum delay limit
    delay = min(delay, config.max_delay)
    
    # Add jitter to avoid thundering herd
    if config.jitter:
        jitter_amount = delay * 0.1 * random.random()
        delay += jitter_amount
    
    return delay


def should_retry(exception: Exception, config: RetryConfig) -> bool:
    """
    Determine if an exception should trigger a retry.
    
    Args:
        exception: Exception that occurred
        config: Retry configuration
        
    Returns:
        True if should retry, False otherwise
    """
    # Never retry certain exceptions
    if isinstance(exception, config.no_retry_exceptions):
        return False
    
    # Check if it's a retryable exception
    if isinstance(exception, config.retry_exceptions):
        return True
    
    # For RateLimitError, check if retry_after is reasonable
    if isinstance(exception, RateLimitError):
        retry_after = getattr(exception, 'retry_after', None)
        if retry_after and retry_after > config.max_delay:
            return False
        return True
    
    return False


def retry_on_error(config: Optional[RetryConfig] = None):
    """
    Decorator for adding retry logic to functions.
    
    Args:
        config: Retry configuration (uses default if None)
        
    Returns:
        Decorated function with retry logic
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(config.max_attempts):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        
                        if not should_retry(e, config):
                            raise
                        
                        if attempt == config.max_attempts - 1:
                            # Last attempt, don't delay
                            break
                        
                        # Handle rate limiting with specific delay
                        if isinstance(e, RateLimitError) and hasattr(e, 'retry_after'):
                            delay = min(e.retry_after, config.max_delay)
                        else:
                            delay = calculate_delay(attempt, config)
                        
                        await asyncio.sleep(delay)
                
                # All attempts failed, raise the last exception
                raise last_exception
            
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(config.max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        
                        if not should_retry(e, config):
                            raise
                        
                        if attempt == config.max_attempts - 1:
                            # Last attempt, don't delay
                            break
                        
                        # Handle rate limiting with specific delay
                        if isinstance(e, RateLimitError) and hasattr(e, 'retry_after'):
                            delay = min(e.retry_after, config.max_delay)
                        else:
                            delay = calculate_delay(attempt, config)
                        
                        time.sleep(delay)
                
                # All attempts failed, raise the last exception
                raise last_exception
            
            return sync_wrapper
    
    return decorator


class RetryManager:
    """
    Context manager for retry operations.
    
    This provides more control over retry logic for complex operations.
    """
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
        self.attempt = 0
        self.last_exception: Optional[Exception] = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_val and should_retry(exc_val, self.config):
            self.last_exception = exc_val
            self.attempt += 1
            
            if self.attempt < self.config.max_attempts:
                delay = calculate_delay(self.attempt - 1, self.config)
                time.sleep(delay)
                return True  # Suppress the exception
        
        return False  # Let the exception propagate
    
    def should_continue(self) -> bool:
        """Check if we should continue retrying."""
        return self.attempt < self.config.max_attempts
    
    def get_delay(self) -> float:
        """Get the delay for the current attempt."""
        return calculate_delay(self.attempt, self.config)


class AsyncRetryManager:
    """
    Async context manager for retry operations.
    """
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
        self.attempt = 0
        self.last_exception: Optional[Exception] = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_val and should_retry(exc_val, self.config):
            self.last_exception = exc_val
            self.attempt += 1
            
            if self.attempt < self.config.max_attempts:
                delay = calculate_delay(self.attempt - 1, self.config)
                await asyncio.sleep(delay)
                return True  # Suppress the exception
        
        return False  # Let the exception propagate
    
    def should_continue(self) -> bool:
        """Check if we should continue retrying."""
        return self.attempt < self.config.max_attempts
    
    def get_delay(self) -> float:
        """Get the delay for the current attempt."""
        return calculate_delay(self.attempt, self.config)


# Predefined retry configurations
AGGRESSIVE_RETRY = RetryConfig(
    max_attempts=5,
    base_delay=0.5,
    max_delay=30.0,
    exponential_base=1.5,
)

CONSERVATIVE_RETRY = RetryConfig(
    max_attempts=2,
    base_delay=2.0,
    max_delay=10.0,
    exponential_base=2.0,
)

RATE_LIMIT_RETRY = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=120.0,
    exponential_base=2.0,
    retry_exceptions=(RateLimitError,),
)

NETWORK_RETRY = RetryConfig(
    max_attempts=4,
    base_delay=1.0,
    max_delay=60.0,
    exponential_base=2.0,
    retry_exceptions=(NetworkError, TimeoutError),
)
