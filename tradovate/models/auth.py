"""
Authentication-related models for the Tradovate SDK.

This module defines models for authentication, tokens, and user sessions.
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pydantic import Field, field_validator

from .common import BaseModel, IDMixin, TimestampMixin, AuditMixin


class AccessToken(BaseModel):
    """Access token model."""
    
    access_token: str = Field(..., alias="accessToken", description="Access token")
    md_access_token: Optional[str] = Field(
        None, 
        alias="mdAccessToken", 
        description="Market data access token"
    )
    token_type: str = Field("Bearer", alias="tokenType", description="Token type")
    expires_in: Optional[int] = Field(
        None, 
        alias="expiresIn", 
        description="Token expiration time in seconds"
    )
    expiration_time: Optional[int] = Field(
        None,
        alias="expirationTime",
        description="Token expiration timestamp"
    )
    scope: Optional[str] = Field(None, description="Token scope")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    
    @property
    def is_expired(self) -> bool:
        """Check if the token is expired."""
        if not self.expiration_time:
            return False
        
        current_time = int(datetime.now(timezone.utc).timestamp())
        return current_time >= self.expiration_time


class User(IDMixin, TimestampMixin, AuditMixin):
    """User model."""
    
    name: Optional[str] = Field(None, description="Username")
    email: Optional[str] = Field(None, description="Email address")
    first_name: Optional[str] = Field(
        None, 
        alias="firstName", 
        description="First name"
    )
    last_name: Optional[str] = Field(
        None, 
        alias="lastName", 
        description="Last name"
    )
    full_name: Optional[str] = Field(
        None, 
        alias="fullName", 
        description="Full name"
    )
    status: Optional[str] = Field(None, description="User status")
    user_type: Optional[str] = Field(
        None, 
        alias="userType", 
        description="User type"
    )
    organization_id: Optional[int] = Field(
        None, 
        alias="organizationId", 
        description="Organization ID"
    )
    professional: Optional[bool] = Field(
        None, 
        description="Professional user flag"
    )
    active: Optional[bool] = Field(None, description="Active user flag")
    confirmed: Optional[bool] = Field(None, description="Confirmed user flag")
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )
    risk_category_id: Optional[int] = Field(
        None,
        alias="riskCategoryId",
        description="Risk category ID"
    )
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format."""
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v


class UserSession(IDMixin, TimestampMixin):
    """User session model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    session_token: Optional[str] = Field(
        None, 
        alias="sessionToken", 
        description="Session token"
    )
    ip_address: Optional[str] = Field(
        None, 
        alias="ipAddress", 
        description="IP address"
    )
    user_agent: Optional[str] = Field(
        None, 
        alias="userAgent", 
        description="User agent"
    )
    login_time: Optional[datetime] = Field(
        None, 
        alias="loginTime", 
        description="Login timestamp"
    )
    last_activity: Optional[datetime] = Field(
        None, 
        alias="lastActivity", 
        description="Last activity timestamp"
    )
    expires_at: Optional[datetime] = Field(
        None, 
        alias="expiresAt", 
        description="Session expiration timestamp"
    )
    active: Optional[bool] = Field(None, description="Active session flag")
    
    @property
    def is_expired(self) -> bool:
        """Check if the session is expired."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) >= self.expires_at


class UserProperty(IDMixin, TimestampMixin):
    """User property model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    property_id: Optional[int] = Field(
        None, 
        alias="propertyId", 
        description="Property ID"
    )
    property_name: Optional[str] = Field(
        None, 
        alias="propertyName", 
        description="Property name"
    )
    value: Optional[str] = Field(None, description="Property value")
    property_type: Optional[str] = Field(
        None, 
        alias="propertyType", 
        description="Property type"
    )


class ContactInfo(IDMixin, TimestampMixin):
    """Contact information model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    first_name: Optional[str] = Field(
        None, 
        alias="firstName", 
        description="First name"
    )
    last_name: Optional[str] = Field(
        None, 
        alias="lastName", 
        description="Last name"
    )
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    mobile: Optional[str] = Field(None, description="Mobile number")
    address1: Optional[str] = Field(None, description="Address line 1")
    address2: Optional[str] = Field(None, description="Address line 2")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State")
    country: Optional[str] = Field(None, description="Country")
    postal_code: Optional[str] = Field(
        None, 
        alias="postalCode", 
        description="Postal code"
    )
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format."""
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v


class Organization(IDMixin, TimestampMixin):
    """Organization model."""
    
    name: Optional[str] = Field(None, description="Organization name")
    master_user_id: Optional[int] = Field(
        None, 
        alias="masterUserId", 
        description="Master user ID"
    )
    nick_name: Optional[str] = Field(
        None, 
        alias="nickName", 
        description="Nickname"
    )
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )


class UserPlugin(IDMixin, TimestampMixin):
    """User plugin model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    plugin_name: Optional[str] = Field(
        None, 
        alias="pluginName", 
        description="Plugin name"
    )
    approval: Optional[str] = Field(None, description="Approval status")
    entitlement_id: Optional[int] = Field(
        None, 
        alias="entitlementId", 
        description="Entitlement ID"
    )
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )


class UserSessionStats(IDMixin, TimestampMixin):
    """User session statistics model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    session_count: Optional[int] = Field(
        None, 
        alias="sessionCount", 
        description="Session count"
    )
    total_time: Optional[int] = Field(
        None, 
        alias="totalTime", 
        description="Total session time in seconds"
    )
    average_time: Optional[float] = Field(
        None, 
        alias="averageTime", 
        description="Average session time in seconds"
    )
    last_login: Optional[datetime] = Field(
        None, 
        alias="lastLogin", 
        description="Last login timestamp"
    )
