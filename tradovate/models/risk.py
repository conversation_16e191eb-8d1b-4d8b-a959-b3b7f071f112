"""
Risk management models for the Tradovate SDK.

This module defines models for risk management and account limits.
"""

from datetime import datetime
from typing import Optional
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class AccountRiskStatus(IDMixin, TimestampMixin):
    """Account risk status model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    auto_liq_status: Optional[str] = Field(
        None,
        alias="autoLiqStatus",
        description="Auto liquidation status"
    )
    daily_loss_limit: Optional[float] = Field(
        None,
        alias="dailyLossLimit",
        description="Daily loss limit"
    )
    daily_loss_limit_breach: Optional[bool] = Field(
        None,
        alias="dailyLossLimitBreach",
        description="Daily loss limit breach flag"
    )
    margin_percentage: Optional[float] = Field(
        None,
        alias="marginPercentage",
        description="Margin percentage"
    )
    margin_percentage_alert: Optional[float] = Field(
        None,
        alias="marginPercentageAlert",
        description="Margin percentage alert level"
    )
    liquidation_only: Optional[bool] = Field(
        None,
        alias="liquidationOnly",
        description="Liquidation only flag"
    )


class UserAccountPositionLimit(IDMixin, TimestampMixin):
    """User account position limit model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    max_long_position: Optional[int] = Field(
        None, 
        alias="maxLongPosition", 
        description="Maximum long position"
    )
    max_short_position: Optional[int] = Field(
        None, 
        alias="maxShortPosition", 
        description="Maximum short position"
    )
    active: Optional[bool] = Field(None, description="Active limit flag")


class UserAccountRiskParameter(IDMixin, TimestampMixin):
    """User account risk parameter model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    parameter_type: Optional[str] = Field(
        None, 
        alias="parameterType", 
        description="Parameter type"
    )
    value: Optional[float] = Field(None, description="Parameter value")
    active: Optional[bool] = Field(None, description="Active parameter flag")


class ContractMargin(IDMixin, TimestampMixin):
    """Contract margin model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    initial_margin: Optional[float] = Field(
        None, 
        alias="initialMargin", 
        description="Initial margin"
    )
    maintenance_margin: Optional[float] = Field(
        None, 
        alias="maintenanceMargin", 
        description="Maintenance margin"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )


class ProductMargin(IDMixin, TimestampMixin):
    """Product margin model."""
    
    product_id: Optional[int] = Field(None, alias="productId", description="Product ID")
    initial_margin: Optional[float] = Field(
        None, 
        alias="initialMargin", 
        description="Initial margin"
    )
    maintenance_margin: Optional[float] = Field(
        None, 
        alias="maintenanceMargin", 
        description="Maintenance margin"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )


class RiskLimit(IDMixin, TimestampMixin):
    """Risk limit model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    limit_type: Optional[str] = Field(
        None, 
        alias="limitType", 
        description="Limit type"
    )
    limit_value: Optional[float] = Field(
        None, 
        alias="limitValue", 
        description="Limit value"
    )
    current_value: Optional[float] = Field(
        None, 
        alias="currentValue", 
        description="Current value"
    )
    breach_status: Optional[str] = Field(
        None, 
        alias="breachStatus", 
        description="Breach status"
    )
    active: Optional[bool] = Field(None, description="Active limit flag")
