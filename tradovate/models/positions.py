"""
Position-related models for the Tradovate SDK.

This module defines models for positions and fill pairs.
"""

from datetime import datetime
from typing import Optional
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Position(IDMixin, TimestampMixin):
    """Position model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    contract_name: Optional[str] = Field(
        None, 
        alias="contractName", 
        description="Contract name"
    )
    net_pos: Optional[int] = Field(None, alias="netPos", description="Net position")
    net_price: Optional[float] = Field(None, alias="netPrice", description="Net price")
    bought: Optional[int] = Field(None, description="Bought quantity")
    bought_value: Optional[float] = Field(
        None, 
        alias="boughtValue", 
        description="Bought value"
    )
    sold: Optional[int] = Field(None, description="Sold quantity")
    sold_value: Optional[float] = Field(
        None, 
        alias="soldValue", 
        description="Sold value"
    )
    prev_pos: Optional[int] = Field(None, alias="prevPos", description="Previous position")
    prev_price: Optional[float] = Field(
        None, 
        alias="prevPrice", 
        description="Previous price"
    )
    unrealized_pnl: Optional[float] = Field(
        None, 
        alias="unrealizedPnL", 
        description="Unrealized P&L"
    )
    realized_pnl: Optional[float] = Field(
        None, 
        alias="realizedPnL", 
        description="Realized P&L"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )


class FillPair(IDMixin, TimestampMixin):
    """Fill pair model."""
    
    fill_id: Optional[int] = Field(None, alias="fillId", description="Fill ID")
    buy_fill_id: Optional[int] = Field(
        None, 
        alias="buyFillId", 
        description="Buy fill ID"
    )
    sell_fill_id: Optional[int] = Field(
        None, 
        alias="sellFillId", 
        description="Sell fill ID"
    )
    qty: Optional[int] = Field(None, description="Quantity")
    price_diff: Optional[float] = Field(
        None, 
        alias="priceDiff", 
        description="Price difference"
    )
    realized_pnl: Optional[float] = Field(
        None, 
        alias="realizedPnL", 
        description="Realized P&L"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
