"""
Common base models and mixins for the Tradovate SDK.

This module provides base classes and common functionality used across
all model definitions.
"""

from datetime import datetime
from typing import Optional, Any, Dict
from pydantic import BaseModel as PydanticBaseModel, Field, ConfigDict


class BaseModel(PydanticBaseModel):
    """Base model class for all Tradovate SDK models."""
    
    model_config = ConfigDict(
        # Allow extra fields for forward compatibility
        extra="allow",
        # Use enum values instead of enum objects
        use_enum_values=True,
        # Validate assignment
        validate_assignment=True,
        # Allow population by field name or alias
        populate_by_name=True,
        # Serialize by alias
        ser_by_alias=True,
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        return self.model_dump(by_alias=True, exclude_none=True)
    
    def to_json(self) -> str:
        """Convert model to JSON string."""
        return self.model_dump_json(by_alias=True, exclude_none=True)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BaseModel":
        """Create model instance from dictionary."""
        return cls.model_validate(data)
    
    @classmethod
    def from_json(cls, json_str: str) -> "BaseModel":
        """Create model instance from JSON string."""
        return cls.model_validate_json(json_str)


class IDMixin(BaseModel):
    """Mixin for models with ID fields."""
    
    id: Optional[int] = Field(None, description="Unique identifier")


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    timestamp: Optional[datetime] = Field(
        None, 
        description="Timestamp when the record was created/updated"
    )


class NamedMixin(BaseModel):
    """Mixin for models with name fields."""
    
    name: Optional[str] = Field(None, description="Name or title")


class DescriptionMixin(BaseModel):
    """Mixin for models with description fields."""
    
    description: Optional[str] = Field(None, description="Description or details")


class StatusMixin(BaseModel):
    """Mixin for models with status fields."""
    
    status: Optional[str] = Field(None, description="Current status")


class PriceMixin(BaseModel):
    """Mixin for models with price-related fields."""
    
    price: Optional[float] = Field(None, description="Price value")
    currency: Optional[str] = Field(None, description="Currency code")


class QuantityMixin(BaseModel):
    """Mixin for models with quantity fields."""
    
    quantity: Optional[int] = Field(None, description="Quantity or size")
    qty: Optional[int] = Field(None, description="Quantity (alias)")


class AccountMixin(BaseModel):
    """Mixin for models associated with accounts."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")


class ContractMixin(BaseModel):
    """Mixin for models associated with contracts."""
    
    contract_id: Optional[int] = Field(
        None, 
        alias="contractId", 
        description="Contract ID"
    )


class UserMixin(BaseModel):
    """Mixin for models associated with users."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")


class AuditMixin(BaseModel):
    """Mixin for models with audit trail information."""
    
    created_at: Optional[datetime] = Field(
        None, 
        alias="createdAt", 
        description="Creation timestamp"
    )
    updated_at: Optional[datetime] = Field(
        None, 
        alias="updatedAt", 
        description="Last update timestamp"
    )
    created_by: Optional[int] = Field(
        None, 
        alias="createdBy", 
        description="ID of user who created the record"
    )
    updated_by: Optional[int] = Field(
        None, 
        alias="updatedBy", 
        description="ID of user who last updated the record"
    )


class PaginationMixin(BaseModel):
    """Mixin for paginated response models."""
    
    page: Optional[int] = Field(None, description="Current page number")
    page_size: Optional[int] = Field(
        None, 
        alias="pageSize", 
        description="Number of items per page"
    )
    total_pages: Optional[int] = Field(
        None, 
        alias="totalPages", 
        description="Total number of pages"
    )
    total_items: Optional[int] = Field(
        None, 
        alias="totalItems", 
        description="Total number of items"
    )


class ErrorMixin(BaseModel):
    """Mixin for error response models."""
    
    error_code: Optional[str] = Field(
        None, 
        alias="errorCode", 
        description="Error code"
    )
    error_message: Optional[str] = Field(
        None, 
        alias="errorMessage", 
        description="Error message"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None, 
        alias="errorDetails", 
        description="Additional error details"
    )


class MetadataMixin(BaseModel):
    """Mixin for models with metadata."""
    
    metadata: Optional[Dict[str, Any]] = Field(
        None, 
        description="Additional metadata"
    )


# Common response wrapper
class APIResponse(BaseModel):
    """Standard API response wrapper."""
    
    success: bool = Field(True, description="Whether the request was successful")
    data: Optional[Any] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if any")
    error_code: Optional[str] = Field(
        None, 
        alias="errorCode", 
        description="Error code if any"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, 
        description="Response timestamp"
    )


# Common list response wrapper
class ListResponse(BaseModel):
    """Standard list response wrapper."""
    
    items: list = Field(default_factory=list, description="List of items")
    total: Optional[int] = Field(None, description="Total number of items")
    page: Optional[int] = Field(None, description="Current page")
    page_size: Optional[int] = Field(
        None, 
        alias="pageSize", 
        description="Items per page"
    )
