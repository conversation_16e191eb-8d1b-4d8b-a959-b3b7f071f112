"""
Market data models for the Tradovate SDK.

This module defines models for market data, quotes, trades, and charts.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Quote(BaseModel):
    """Quote model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    bid: Optional[float] = Field(None, description="Bid price")
    ask: Optional[float] = Field(None, description="Ask price")
    bid_size: Optional[int] = Field(None, alias="bidSize", description="Bid size")
    ask_size: Optional[int] = Field(None, alias="askSize", description="Ask size")
    timestamp: Optional[datetime] = Field(None, description="Quote timestamp")


class Trade(BaseModel):
    """Trade model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    price: Optional[float] = Field(None, description="Trade price")
    size: Optional[int] = Field(None, description="Trade size")
    timestamp: Optional[datetime] = Field(None, description="Trade timestamp")
    aggressor: Optional[str] = Field(None, description="Aggressor side")


class MarketData(BaseModel):
    """Market data model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    last_price: Optional[float] = Field(None, alias="lastPrice", description="Last price")
    last_size: Optional[int] = Field(None, alias="lastSize", description="Last size")
    bid: Optional[float] = Field(None, description="Bid price")
    ask: Optional[float] = Field(None, description="Ask price")
    bid_size: Optional[int] = Field(None, alias="bidSize", description="Bid size")
    ask_size: Optional[int] = Field(None, alias="askSize", description="Ask size")
    high: Optional[float] = Field(None, description="High price")
    low: Optional[float] = Field(None, description="Low price")
    open_price: Optional[float] = Field(None, alias="openPrice", description="Open price")
    close_price: Optional[float] = Field(None, alias="closePrice", description="Close price")
    volume: Optional[int] = Field(None, description="Volume")
    open_interest: Optional[int] = Field(None, alias="openInterest", description="Open interest")
    timestamp: Optional[datetime] = Field(None, description="Data timestamp")


class Chart(BaseModel):
    """Chart data model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    timestamp: Optional[datetime] = Field(None, description="Bar timestamp")
    open_price: Optional[float] = Field(None, alias="openPrice", description="Open price")
    high_price: Optional[float] = Field(None, alias="highPrice", description="High price")
    low_price: Optional[float] = Field(None, alias="lowPrice", description="Low price")
    close_price: Optional[float] = Field(None, alias="closePrice", description="Close price")
    volume: Optional[int] = Field(None, description="Volume")
    up_volume: Optional[int] = Field(None, alias="upVolume", description="Up volume")
    down_volume: Optional[int] = Field(None, alias="downVolume", description="Down volume")
    up_ticks: Optional[int] = Field(None, alias="upTicks", description="Up ticks")
    down_ticks: Optional[int] = Field(None, alias="downTicks", description="Down ticks")
    bid_volume: Optional[int] = Field(None, alias="bidVolume", description="Bid volume")
    ask_volume: Optional[int] = Field(None, alias="askVolume", description="Ask volume")


class DepthOfMarket(BaseModel):
    """Depth of market model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    bids: Optional[List[dict]] = Field(None, description="Bid levels")
    asks: Optional[List[dict]] = Field(None, description="Ask levels")
    timestamp: Optional[datetime] = Field(None, description="DOM timestamp")


class MarketDataLevel(BaseModel):
    """Market data level model for DOM."""
    
    price: Optional[float] = Field(None, description="Price level")
    size: Optional[int] = Field(None, description="Size at level")
    orders: Optional[int] = Field(None, description="Number of orders")


class Tick(BaseModel):
    """Tick data model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    price: Optional[float] = Field(None, description="Tick price")
    size: Optional[int] = Field(None, description="Tick size")
    timestamp: Optional[datetime] = Field(None, description="Tick timestamp")
    tick_type: Optional[str] = Field(None, alias="tickType", description="Tick type")


class HistoricalData(BaseModel):
    """Historical data model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    bars: Optional[List[Chart]] = Field(None, description="Historical bars")
    interval: Optional[str] = Field(None, description="Data interval")
    start_time: Optional[datetime] = Field(None, alias="startTime", description="Start time")
    end_time: Optional[datetime] = Field(None, alias="endTime", description="End time")
