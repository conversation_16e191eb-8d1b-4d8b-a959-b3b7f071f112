"""
Order-related models for the Tradovate SDK.

This module defines models for orders, fills, and execution reports.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin, AuditMixin


class Order(IDMixin, TimestampMixin, AuditMixin):
    """Order model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    contract_name: Optional[str] = Field(
        None, 
        alias="contractName", 
        description="Contract name"
    )
    action: Optional[str] = Field(None, description="Order action (Buy/Sell)")
    order_type: Optional[str] = Field(
        None, 
        alias="orderType", 
        description="Order type"
    )
    qty: Optional[int] = Field(None, description="Order quantity")
    filled_qty: Optional[int] = Field(
        None, 
        alias="filledQty", 
        description="Filled quantity"
    )
    remaining_qty: Optional[int] = Field(
        None, 
        alias="remainingQty", 
        description="Remaining quantity"
    )
    price: Optional[float] = Field(None, description="Order price")
    stop_price: Optional[float] = Field(
        None, 
        alias="stopPrice", 
        description="Stop price"
    )
    time_in_force: Optional[str] = Field(
        None, 
        alias="timeInForce", 
        description="Time in force"
    )
    order_status: Optional[str] = Field(
        None, 
        alias="orderStatus", 
        description="Order status"
    )
    is_automated: Optional[bool] = Field(
        None, 
        alias="isAutomated", 
        description="Automated order flag"
    )
    order_version: Optional[int] = Field(
        None, 
        alias="orderVersion", 
        description="Order version"
    )
    cl_ord_id: Optional[str] = Field(
        None, 
        alias="clOrdId", 
        description="Client order ID"
    )


class Fill(IDMixin, TimestampMixin):
    """Fill model."""
    
    order_id: Optional[int] = Field(None, alias="orderId", description="Order ID")
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    exec_id: Optional[str] = Field(None, alias="execId", description="Execution ID")
    action: Optional[str] = Field(None, description="Fill action")
    qty: Optional[int] = Field(None, description="Fill quantity")
    price: Optional[float] = Field(None, description="Fill price")
    active: Optional[bool] = Field(None, description="Active fill flag")
    final: Optional[bool] = Field(None, description="Final fill flag")


class ExecutionReport(IDMixin, TimestampMixin):
    """Execution report model."""
    
    order_id: Optional[int] = Field(None, alias="orderId", description="Order ID")
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    exec_type: Optional[str] = Field(None, alias="execType", description="Execution type")
    exec_ref_id: Optional[str] = Field(
        None, 
        alias="execRefId", 
        description="Execution reference ID"
    )
    order_status: Optional[str] = Field(
        None, 
        alias="orderStatus", 
        description="Order status"
    )
    action: Optional[str] = Field(None, description="Order action")
    qty: Optional[int] = Field(None, description="Order quantity")
    price: Optional[float] = Field(None, description="Order price")
    stop_price: Optional[float] = Field(
        None, 
        alias="stopPrice", 
        description="Stop price"
    )
    order_type: Optional[str] = Field(
        None, 
        alias="orderType", 
        description="Order type"
    )
    time_in_force: Optional[str] = Field(
        None, 
        alias="timeInForce", 
        description="Time in force"
    )
    exec_qty: Optional[int] = Field(None, alias="execQty", description="Executed quantity")
    exec_price: Optional[float] = Field(
        None, 
        alias="execPrice", 
        description="Executed price"
    )
    leaves_qty: Optional[int] = Field(
        None, 
        alias="leavesQty", 
        description="Leaves quantity"
    )
    cum_qty: Optional[int] = Field(None, alias="cumQty", description="Cumulative quantity")
    avg_price: Optional[float] = Field(None, alias="avgPrice", description="Average price")
    reject_reason: Optional[str] = Field(
        None, 
        alias="rejectReason", 
        description="Rejection reason"
    )
    text: Optional[str] = Field(None, description="Execution text")


class OrderStrategy(IDMixin, TimestampMixin):
    """Order strategy model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    strategy_type: Optional[str] = Field(
        None, 
        alias="strategyType", 
        description="Strategy type"
    )
    status: Optional[str] = Field(None, description="Strategy status")
    action: Optional[str] = Field(None, description="Strategy action")
    params: Optional[str] = Field(None, description="Strategy parameters")
    uuid: Optional[str] = Field(None, description="Strategy UUID")


class OrderVersion(IDMixin, TimestampMixin):
    """Order version model."""
    
    order_id: Optional[int] = Field(None, alias="orderId", description="Order ID")
    order_version: Optional[int] = Field(
        None, 
        alias="orderVersion", 
        description="Order version"
    )
    qty: Optional[int] = Field(None, description="Order quantity")
    price: Optional[float] = Field(None, description="Order price")
    stop_price: Optional[float] = Field(
        None, 
        alias="stopPrice", 
        description="Stop price"
    )
    order_type: Optional[str] = Field(
        None, 
        alias="orderType", 
        description="Order type"
    )
    time_in_force: Optional[str] = Field(
        None, 
        alias="timeInForce", 
        description="Time in force"
    )


class FillFee(IDMixin, TimestampMixin):
    """Fill fee model."""
    
    fill_id: Optional[int] = Field(None, alias="fillId", description="Fill ID")
    order_id: Optional[int] = Field(None, alias="orderId", description="Order ID")
    exec_id: Optional[str] = Field(None, alias="execId", description="Execution ID")
    fee: Optional[float] = Field(None, description="Fee amount")
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )


class Command(IDMixin, TimestampMixin):
    """Command model."""
    
    command: Optional[str] = Field(None, description="Command string")
    data: Optional[str] = Field(None, description="Command data")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")


class CommandReport(IDMixin, TimestampMixin):
    """Command report model."""
    
    command_id: Optional[int] = Field(None, alias="commandId", description="Command ID")
    result_code: Optional[int] = Field(
        None, 
        alias="resultCode", 
        description="Result code"
    )
    result_text: Optional[str] = Field(
        None, 
        alias="resultText", 
        description="Result text"
    )
    response_type: Optional[str] = Field(
        None, 
        alias="responseType", 
        description="Response type"
    )
