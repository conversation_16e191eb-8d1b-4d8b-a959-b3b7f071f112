"""
Command-related models for the Tradovate SDK.

This module defines models for command execution and reporting.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Command(IDMixin, TimestampMixin):
    """Command model."""
    
    name: Optional[str] = Field(None, description="Command name")
    command_type: Optional[str] = Field(
        None, 
        alias="commandType", 
        description="Command type"
    )
    description: Optional[str] = Field(None, description="Command description")
    parameters: Optional[Dict[str, Any]] = Field(
        None, 
        description="Command parameters"
    )
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    account_id: Optional[int] = Field(
        None, 
        alias="accountId", 
        description="Account ID"
    )
    status: Optional[str] = Field(None, description="Command status")
    executed_at: Optional[datetime] = Field(
        None, 
        alias="executedAt", 
        description="Execution timestamp"
    )
    completed_at: Optional[datetime] = Field(
        None, 
        alias="completedAt", 
        description="Completion timestamp"
    )
    result: Optional[Dict[str, Any]] = Field(
        None, 
        description="Command result"
    )
    error_message: Optional[str] = Field(
        None, 
        alias="errorMessage", 
        description="Error message"
    )
    retry_count: Optional[int] = Field(
        None, 
        alias="retryCount", 
        description="Retry count"
    )
    max_retries: Optional[int] = Field(
        None, 
        alias="maxRetries", 
        description="Maximum retries"
    )
    priority: Optional[int] = Field(None, description="Command priority")
    
    @property
    def is_completed(self) -> bool:
        """Check if the command is completed."""
        return self.status in ["completed", "success", "failed"]
    
    @property
    def is_successful(self) -> bool:
        """Check if the command was successful."""
        return self.status in ["completed", "success"]
    
    @property
    def is_failed(self) -> bool:
        """Check if the command failed."""
        return self.status == "failed"


class CommandReport(IDMixin, TimestampMixin):
    """Command report model."""
    
    command_id: Optional[int] = Field(
        None, 
        alias="commandId", 
        description="Command ID"
    )
    report_type: Optional[str] = Field(
        None, 
        alias="reportType", 
        description="Report type"
    )
    title: Optional[str] = Field(None, description="Report title")
    description: Optional[str] = Field(None, description="Report description")
    data: Optional[Dict[str, Any]] = Field(
        None, 
        description="Report data"
    )
    format: Optional[str] = Field(None, description="Report format")
    file_path: Optional[str] = Field(
        None, 
        alias="filePath", 
        description="Report file path"
    )
    file_size: Optional[int] = Field(
        None, 
        alias="fileSize", 
        description="Report file size"
    )
    generated_at: Optional[datetime] = Field(
        None, 
        alias="generatedAt", 
        description="Generation timestamp"
    )
    expires_at: Optional[datetime] = Field(
        None, 
        alias="expiresAt", 
        description="Expiration timestamp"
    )
    download_count: Optional[int] = Field(
        None, 
        alias="downloadCount", 
        description="Download count"
    )
    status: Optional[str] = Field(None, description="Report status")
    
    @property
    def is_expired(self) -> bool:
        """Check if the report is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() >= self.expires_at
    
    @property
    def is_available(self) -> bool:
        """Check if the report is available for download."""
        return (
            self.status == "completed" and 
            not self.is_expired and 
            self.file_path is not None
        )


class CommandTemplate(IDMixin, TimestampMixin):
    """Command template model."""
    
    name: Optional[str] = Field(None, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    command_type: Optional[str] = Field(
        None, 
        alias="commandType", 
        description="Command type"
    )
    template_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="templateData", 
        description="Template data"
    )
    parameters_schema: Optional[Dict[str, Any]] = Field(
        None, 
        alias="parametersSchema", 
        description="Parameters schema"
    )
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    shared: Optional[bool] = Field(None, description="Shared template flag")
    active: Optional[bool] = Field(None, description="Active template flag")
    usage_count: Optional[int] = Field(
        None, 
        alias="usageCount", 
        description="Usage count"
    )
    last_used_at: Optional[datetime] = Field(
        None, 
        alias="lastUsedAt", 
        description="Last used timestamp"
    )


class CommandSchedule(IDMixin, TimestampMixin):
    """Command schedule model."""
    
    command_template_id: Optional[int] = Field(
        None, 
        alias="commandTemplateId", 
        description="Command template ID"
    )
    name: Optional[str] = Field(None, description="Schedule name")
    description: Optional[str] = Field(None, description="Schedule description")
    schedule_type: Optional[str] = Field(
        None, 
        alias="scheduleType", 
        description="Schedule type (once, recurring)"
    )
    cron_expression: Optional[str] = Field(
        None, 
        alias="cronExpression", 
        description="Cron expression"
    )
    timezone: Optional[str] = Field(None, description="Timezone")
    start_date: Optional[datetime] = Field(
        None, 
        alias="startDate", 
        description="Start date"
    )
    end_date: Optional[datetime] = Field(
        None, 
        alias="endDate", 
        description="End date"
    )
    next_run_at: Optional[datetime] = Field(
        None, 
        alias="nextRunAt", 
        description="Next run timestamp"
    )
    last_run_at: Optional[datetime] = Field(
        None, 
        alias="lastRunAt", 
        description="Last run timestamp"
    )
    run_count: Optional[int] = Field(
        None, 
        alias="runCount", 
        description="Run count"
    )
    max_runs: Optional[int] = Field(
        None, 
        alias="maxRuns", 
        description="Maximum runs"
    )
    active: Optional[bool] = Field(None, description="Active schedule flag")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    
    @property
    def is_active(self) -> bool:
        """Check if the schedule is active."""
        if not self.active:
            return False
        if self.end_date and datetime.utcnow() > self.end_date:
            return False
        if self.max_runs and self.run_count and self.run_count >= self.max_runs:
            return False
        return True


class CommandExecution(IDMixin, TimestampMixin):
    """Command execution model."""
    
    command_id: Optional[int] = Field(
        None, 
        alias="commandId", 
        description="Command ID"
    )
    schedule_id: Optional[int] = Field(
        None, 
        alias="scheduleId", 
        description="Schedule ID"
    )
    execution_id: Optional[str] = Field(
        None, 
        alias="executionId", 
        description="Execution ID"
    )
    status: Optional[str] = Field(None, description="Execution status")
    started_at: Optional[datetime] = Field(
        None, 
        alias="startedAt", 
        description="Started timestamp"
    )
    completed_at: Optional[datetime] = Field(
        None, 
        alias="completedAt", 
        description="Completed timestamp"
    )
    duration_ms: Optional[int] = Field(
        None, 
        alias="durationMs", 
        description="Duration in milliseconds"
    )
    result: Optional[Dict[str, Any]] = Field(
        None, 
        description="Execution result"
    )
    error_message: Optional[str] = Field(
        None, 
        alias="errorMessage", 
        description="Error message"
    )
    logs: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="Execution logs"
    )
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get duration in seconds."""
        if self.duration_ms is None:
            return None
        return self.duration_ms / 1000.0
    
    @property
    def is_running(self) -> bool:
        """Check if the execution is running."""
        return self.status == "running" and not self.completed_at
