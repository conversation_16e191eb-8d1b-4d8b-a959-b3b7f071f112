"""
Alert-related models for the Tradovate SDK.

This module defines models for alerts and alert signals.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Alert(IDMixin, TimestampMixin):
    """Alert model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    alert_type: Optional[str] = Field(
        None, 
        alias="alertType", 
        description="Alert type"
    )
    message: Optional[str] = Field(None, description="Alert message")
    title: Optional[str] = Field(None, description="Alert title")
    priority: Optional[str] = Field(None, description="Alert priority")
    status: Optional[str] = Field(None, description="Alert status")
    contract_id: Optional[int] = Field(
        None, 
        alias="contractId", 
        description="Contract ID"
    )
    account_id: Optional[int] = Field(
        None, 
        alias="accountId", 
        description="Account ID"
    )
    trigger_price: Optional[float] = Field(
        None, 
        alias="triggerPrice", 
        description="Trigger price"
    )
    trigger_condition: Optional[str] = Field(
        None, 
        alias="triggerCondition", 
        description="Trigger condition"
    )
    triggered_at: Optional[datetime] = Field(
        None, 
        alias="triggeredAt", 
        description="Triggered timestamp"
    )
    expires_at: Optional[datetime] = Field(
        None, 
        alias="expiresAt", 
        description="Expiration timestamp"
    )
    active: Optional[bool] = Field(None, description="Active alert flag")
    dismissed: Optional[bool] = Field(None, description="Dismissed flag")
    dismissed_at: Optional[datetime] = Field(
        None, 
        alias="dismissedAt", 
        description="Dismissed timestamp"
    )
    alert_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="alertData", 
        description="Additional alert data"
    )
    
    @property
    def is_triggered(self) -> bool:
        """Check if the alert has been triggered."""
        return self.triggered_at is not None
    
    @property
    def is_expired(self) -> bool:
        """Check if the alert is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() >= self.expires_at
    
    @property
    def is_active(self) -> bool:
        """Check if the alert is active."""
        return (
            self.active is True and 
            not self.dismissed and 
            not self.is_expired and 
            not self.is_triggered
        )


class AlertSignal(IDMixin, TimestampMixin):
    """Alert signal model."""
    
    alert_id: Optional[int] = Field(None, alias="alertId", description="Alert ID")
    signal_type: Optional[str] = Field(
        None, 
        alias="signalType", 
        description="Signal type"
    )
    signal_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="signalData", 
        description="Signal data"
    )
    triggered_price: Optional[float] = Field(
        None, 
        alias="triggeredPrice", 
        description="Price that triggered the signal"
    )
    market_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="marketData", 
        description="Market data at trigger time"
    )
    processed: Optional[bool] = Field(None, description="Processed flag")
    processed_at: Optional[datetime] = Field(
        None, 
        alias="processedAt", 
        description="Processed timestamp"
    )


class AlertTemplate(IDMixin, TimestampMixin):
    """Alert template model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    name: Optional[str] = Field(None, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    alert_type: Optional[str] = Field(
        None, 
        alias="alertType", 
        description="Alert type"
    )
    template_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="templateData", 
        description="Template configuration data"
    )
    active: Optional[bool] = Field(None, description="Active template flag")


class AlertHistory(IDMixin, TimestampMixin):
    """Alert history model."""
    
    alert_id: Optional[int] = Field(None, alias="alertId", description="Alert ID")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    action: Optional[str] = Field(None, description="Action performed")
    action_data: Optional[Dict[str, Any]] = Field(
        None, 
        alias="actionData", 
        description="Action data"
    )
    performed_by: Optional[int] = Field(
        None, 
        alias="performedBy", 
        description="User who performed the action"
    )
    notes: Optional[str] = Field(None, description="Additional notes")


class AlertSubscription(IDMixin, TimestampMixin):
    """Alert subscription model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    subscription_type: Optional[str] = Field(
        None, 
        alias="subscriptionType", 
        description="Subscription type"
    )
    delivery_method: Optional[str] = Field(
        None, 
        alias="deliveryMethod", 
        description="Delivery method (email, sms, push)"
    )
    delivery_address: Optional[str] = Field(
        None, 
        alias="deliveryAddress", 
        description="Delivery address"
    )
    filter_criteria: Optional[Dict[str, Any]] = Field(
        None, 
        alias="filterCriteria", 
        description="Filter criteria"
    )
    active: Optional[bool] = Field(None, description="Active subscription flag")
    verified: Optional[bool] = Field(None, description="Verified flag")
    verified_at: Optional[datetime] = Field(
        None, 
        alias="verifiedAt", 
        description="Verification timestamp"
    )


class AlertNotification(IDMixin, TimestampMixin):
    """Alert notification model."""
    
    alert_id: Optional[int] = Field(None, alias="alertId", description="Alert ID")
    subscription_id: Optional[int] = Field(
        None, 
        alias="subscriptionId", 
        description="Subscription ID"
    )
    delivery_method: Optional[str] = Field(
        None, 
        alias="deliveryMethod", 
        description="Delivery method"
    )
    delivery_address: Optional[str] = Field(
        None, 
        alias="deliveryAddress", 
        description="Delivery address"
    )
    status: Optional[str] = Field(None, description="Notification status")
    sent_at: Optional[datetime] = Field(
        None, 
        alias="sentAt", 
        description="Sent timestamp"
    )
    delivered_at: Optional[datetime] = Field(
        None, 
        alias="deliveredAt", 
        description="Delivered timestamp"
    )
    error_message: Optional[str] = Field(
        None, 
        alias="errorMessage", 
        description="Error message if delivery failed"
    )
    retry_count: Optional[int] = Field(
        None, 
        alias="retryCount", 
        description="Retry count"
    )
    max_retries: Optional[int] = Field(
        None, 
        alias="maxRetries", 
        description="Maximum retries"
    )
