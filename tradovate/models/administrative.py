"""
Administrative models for the Tradovate SDK.

This module defines models for clearing houses, entitlements, properties, and organizations.
"""

from datetime import datetime
from typing import Optional
from pydantic import Field

from .common import IDMixin, TimestampMixin


class ClearingHouse(IDMixin, TimestampMixin):
    """Clearing house model."""
    
    name: Optional[str] = Field(None, description="Clearing house name")
    clearing_house_code: Optional[str] = Field(
        None, 
        alias="clearingHouseCode", 
        description="Clearing house code"
    )
    active: Optional[bool] = Field(None, description="Active clearing house flag")


class Entitlement(IDMixin, TimestampMixin):
    """Entitlement model."""
    
    name: Optional[str] = Field(None, description="Entitlement name")
    description: Optional[str] = Field(None, description="Description")
    entitlement_type: Optional[str] = Field(
        None, 
        alias="entitlementType", 
        description="Entitlement type"
    )
    active: Optional[bool] = Field(None, description="Active entitlement flag")


class Property(IDMixin, TimestampMixin):
    """Property model."""
    
    name: Optional[str] = Field(None, description="Property name")
    property_type: Optional[str] = Field(
        None, 
        alias="propertyType", 
        description="Property type"
    )
    default_value: Optional[str] = Field(
        None, 
        alias="defaultValue", 
        description="Default value"
    )
    description: Optional[str] = Field(None, description="Description")
    active: Optional[bool] = Field(None, description="Active property flag")


class Organization(IDMixin, TimestampMixin):
    """Organization model."""
    
    name: Optional[str] = Field(None, description="Organization name")
    master_user_id: Optional[int] = Field(
        None, 
        alias="masterUserId", 
        description="Master user ID"
    )
    nick_name: Optional[str] = Field(
        None, 
        alias="nickName", 
        description="Nickname"
    )
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )
    active: Optional[bool] = Field(None, description="Active organization flag")


class UserProperty(IDMixin, TimestampMixin):
    """User property model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    property_id: Optional[int] = Field(
        None, 
        alias="propertyId", 
        description="Property ID"
    )
    property_name: Optional[str] = Field(
        None, 
        alias="propertyName", 
        description="Property name"
    )
    value: Optional[str] = Field(None, description="Property value")
    property_type: Optional[str] = Field(
        None, 
        alias="propertyType", 
        description="Property type"
    )


class UserPlugin(IDMixin, TimestampMixin):
    """User plugin model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    plugin_name: Optional[str] = Field(
        None, 
        alias="pluginName", 
        description="Plugin name"
    )
    approval: Optional[str] = Field(None, description="Approval status")
    entitlement_id: Optional[int] = Field(
        None, 
        alias="entitlementId", 
        description="Entitlement ID"
    )
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )


class TradingPermission(IDMixin, TimestampMixin):
    """Trading permission model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_group_id: Optional[int] = Field(
        None, 
        alias="contractGroupId", 
        description="Contract group ID"
    )
    status: Optional[str] = Field(None, description="Permission status")
    ctc_permission: Optional[bool] = Field(
        None,
        alias="ctcPermission",
        description="CTC permission flag"
    )
    auto_liq: Optional[bool] = Field(
        None,
        alias="autoLiq",
        description="Auto liquidation flag"
    )


class UserAccountPositionLimit(IDMixin, TimestampMixin):
    """User account position limit model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    max_long_position: Optional[int] = Field(
        None, 
        alias="maxLongPosition", 
        description="Maximum long position"
    )
    max_short_position: Optional[int] = Field(
        None, 
        alias="maxShortPosition", 
        description="Maximum short position"
    )
    active: Optional[bool] = Field(None, description="Active limit flag")


class UserAccountRiskParameter(IDMixin, TimestampMixin):
    """User account risk parameter model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    parameter_type: Optional[str] = Field(
        None, 
        alias="parameterType", 
        description="Parameter type"
    )
    value: Optional[float] = Field(None, description="Parameter value")
    active: Optional[bool] = Field(None, description="Active parameter flag")


class MarketDataSubscription(IDMixin, TimestampMixin):
    """Market data subscription model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    timestamp: Optional[datetime] = Field(None, description="Subscription timestamp")
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )
    credit_card_transaction_id: Optional[int] = Field(
        None,
        alias="creditCardTransactionId",
        description="Credit card transaction ID"
    )
    cash_balance_log_id: Optional[int] = Field(
        None,
        alias="cashBalanceLogId",
        description="Cash balance log ID"
    )
    credit_card_id: Optional[int] = Field(
        None,
        alias="creditCardId",
        description="Credit card ID"
    )
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    active: Optional[bool] = Field(None, description="Active subscription flag")


class SecondMarketDataSubscription(IDMixin, TimestampMixin):
    """Second market data subscription model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    timestamp: Optional[datetime] = Field(None, description="Subscription timestamp")
    plan_price: Optional[float] = Field(
        None, 
        alias="planPrice", 
        description="Plan price"
    )
    credit_card_transaction_id: Optional[int] = Field(
        None,
        alias="creditCardTransactionId",
        description="Credit card transaction ID"
    )
    cash_balance_log_id: Optional[int] = Field(
        None,
        alias="cashBalanceLogId",
        description="Cash balance log ID"
    )
    credit_card_id: Optional[int] = Field(
        None,
        alias="creditCardId",
        description="Credit card ID"
    )
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    active: Optional[bool] = Field(None, description="Active subscription flag")


class ReplaySession(IDMixin, TimestampMixin):
    """Replay session model."""
    
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    start_timestamp: Optional[datetime] = Field(
        None, 
        alias="startTimestamp", 
        description="Start timestamp"
    )
    end_timestamp: Optional[datetime] = Field(
        None, 
        alias="endTimestamp", 
        description="End timestamp"
    )
    speed: Optional[int] = Field(None, description="Replay speed")
    status: Optional[str] = Field(None, description="Session status")
    current_timestamp: Optional[datetime] = Field(
        None,
        alias="currentTimestamp",
        description="Current timestamp"
    )


class CreditCard(IDMixin, TimestampMixin):
    """Credit card model."""

    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    card_type: Optional[str] = Field(
        None,
        alias="cardType",
        description="Card type"
    )
    last_four_digits: Optional[str] = Field(
        None,
        alias="lastFourDigits",
        description="Last four digits"
    )
    expiry_month: Optional[int] = Field(
        None,
        alias="expiryMonth",
        description="Expiry month"
    )
    expiry_year: Optional[int] = Field(
        None,
        alias="expiryYear",
        description="Expiry year"
    )
    cardholder_name: Optional[str] = Field(
        None,
        alias="cardholderName",
        description="Cardholder name"
    )
    billing_address: Optional[str] = Field(
        None,
        alias="billingAddress",
        description="Billing address"
    )
    active: Optional[bool] = Field(None, description="Active card flag")
    verified: Optional[bool] = Field(None, description="Verified flag")
    default: Optional[bool] = Field(None, description="Default card flag")


class CreditCardTransaction(IDMixin, TimestampMixin):
    """Credit card transaction model."""

    credit_card_id: Optional[int] = Field(
        None,
        alias="creditCardId",
        description="Credit card ID"
    )
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    amount: Optional[float] = Field(None, description="Transaction amount")
    currency: Optional[str] = Field(None, description="Currency")
    transaction_type: Optional[str] = Field(
        None,
        alias="transactionType",
        description="Transaction type"
    )
    status: Optional[str] = Field(None, description="Transaction status")
    reference_id: Optional[str] = Field(
        None,
        alias="referenceId",
        description="Reference ID"
    )
    description: Optional[str] = Field(None, description="Transaction description")
    processed_at: Optional[datetime] = Field(
        None,
        alias="processedAt",
        description="Processed timestamp"
    )
    error_message: Optional[str] = Field(
        None,
        alias="errorMessage",
        description="Error message"
    )


class Subscription(IDMixin, TimestampMixin):
    """Subscription model."""

    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    subscription_type: Optional[str] = Field(
        None,
        alias="subscriptionType",
        description="Subscription type"
    )
    plan_name: Optional[str] = Field(
        None,
        alias="planName",
        description="Plan name"
    )
    plan_price: Optional[float] = Field(
        None,
        alias="planPrice",
        description="Plan price"
    )
    billing_cycle: Optional[str] = Field(
        None,
        alias="billingCycle",
        description="Billing cycle"
    )
    start_date: Optional[datetime] = Field(
        None,
        alias="startDate",
        description="Start date"
    )
    end_date: Optional[datetime] = Field(
        None,
        alias="endDate",
        description="End date"
    )
    next_billing_date: Optional[datetime] = Field(
        None,
        alias="nextBillingDate",
        description="Next billing date"
    )
    status: Optional[str] = Field(None, description="Subscription status")
    auto_renew: Optional[bool] = Field(
        None,
        alias="autoRenew",
        description="Auto renew flag"
    )
    trial_period: Optional[bool] = Field(
        None,
        alias="trialPeriod",
        description="Trial period flag"
    )
    trial_end_date: Optional[datetime] = Field(
        None,
        alias="trialEndDate",
        description="Trial end date"
    )
