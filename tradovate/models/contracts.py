"""
Contract-related models for the Tradovate SDK.

This module defines models for contracts, contract groups, and related entities.
"""

from datetime import datetime
from typing import Optional
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Contract(IDMixin, TimestampMixin):
    """Contract model."""
    
    name: Optional[str] = Field(None, description="Contract name")
    contract_maturity_id: Optional[int] = Field(
        None, 
        alias="contractMaturityId", 
        description="Contract maturity ID"
    )
    status: Optional[str] = Field(None, description="Contract status")
    master_id: Optional[int] = Field(
        None, 
        alias="masterId", 
        description="Master ID"
    )
    product_id: Optional[int] = Field(
        None, 
        alias="productId", 
        description="Product ID"
    )
    product_name: Optional[str] = Field(
        None, 
        alias="productName", 
        description="Product name"
    )
    tick_size: Optional[float] = Field(
        None, 
        alias="tickSize", 
        description="Tick size"
    )
    tick_value: Optional[float] = Field(
        None, 
        alias="tickValue", 
        description="Tick value"
    )
    point_value: Optional[float] = Field(
        None, 
        alias="pointValue", 
        description="Point value"
    )
    expiry_date: Optional[datetime] = Field(
        None, 
        alias="expiryDate", 
        description="Expiry date"
    )
    first_notice_date: Optional[datetime] = Field(
        None, 
        alias="firstNoticeDate", 
        description="First notice date"
    )
    last_trading_date: Optional[datetime] = Field(
        None, 
        alias="lastTradingDate", 
        description="Last trading date"
    )


class ContractGroup(IDMixin, TimestampMixin):
    """Contract group model."""
    
    name: Optional[str] = Field(None, description="Contract group name")
    description: Optional[str] = Field(None, description="Description")
    active: Optional[bool] = Field(None, description="Active group flag")


class ContractMaturity(IDMixin, TimestampMixin):
    """Contract maturity model."""
    
    contract_id: Optional[int] = Field(
        None, 
        alias="contractId", 
        description="Contract ID"
    )
    month: Optional[int] = Field(None, description="Maturity month")
    year: Optional[int] = Field(None, description="Maturity year")
    maturity_date: Optional[datetime] = Field(
        None, 
        alias="maturityDate", 
        description="Maturity date"
    )


class Product(IDMixin, TimestampMixin):
    """Product model."""
    
    name: Optional[str] = Field(None, description="Product name")
    exchange_id: Optional[int] = Field(
        None, 
        alias="exchangeId", 
        description="Exchange ID"
    )
    contract_group_id: Optional[int] = Field(
        None, 
        alias="contractGroupId", 
        description="Contract group ID"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
    product_type: Optional[str] = Field(
        None, 
        alias="productType", 
        description="Product type"
    )
    tick_size: Optional[float] = Field(
        None, 
        alias="tickSize", 
        description="Tick size"
    )
    tick_value: Optional[float] = Field(
        None, 
        alias="tickValue", 
        description="Tick value"
    )
    point_value: Optional[float] = Field(
        None, 
        alias="pointValue", 
        description="Point value"
    )


class Exchange(IDMixin, TimestampMixin):
    """Exchange model."""
    
    name: Optional[str] = Field(None, description="Exchange name")
    exchange_code: Optional[str] = Field(
        None, 
        alias="exchangeCode", 
        description="Exchange code"
    )
    time_zone: Optional[str] = Field(
        None, 
        alias="timeZone", 
        description="Time zone"
    )
    country: Optional[str] = Field(None, description="Country")
    active: Optional[bool] = Field(None, description="Active exchange flag")
