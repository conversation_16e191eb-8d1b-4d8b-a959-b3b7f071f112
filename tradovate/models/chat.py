"""
Chat-related models for the Tradovate SDK.

This module defines models for chat functionality and messaging.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin


class Chat(IDMixin, TimestampMixin):
    """Chat model."""
    
    name: Optional[str] = Field(None, description="Chat name")
    description: Optional[str] = Field(None, description="Chat description")
    chat_type: Optional[str] = Field(
        None, 
        alias="chatType", 
        description="Chat type (public, private, group)"
    )
    owner_id: Optional[int] = Field(
        None, 
        alias="ownerId", 
        description="Chat owner user ID"
    )
    participant_count: Optional[int] = Field(
        None, 
        alias="participantCount", 
        description="Number of participants"
    )
    last_message_id: Optional[int] = Field(
        None, 
        alias="lastMessageId", 
        description="Last message ID"
    )
    last_message_at: Optional[datetime] = Field(
        None, 
        alias="lastMessageAt", 
        description="Last message timestamp"
    )
    active: Optional[bool] = Field(None, description="Active chat flag")
    archived: Optional[bool] = Field(None, description="Archived flag")
    archived_at: Optional[datetime] = Field(
        None, 
        alias="archivedAt", 
        description="Archived timestamp"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None, 
        description="Chat settings"
    )
    
    @property
    def is_active(self) -> bool:
        """Check if the chat is active."""
        return self.active is True and not self.archived


class ChatMessage(IDMixin, TimestampMixin):
    """Chat message model."""
    
    chat_id: Optional[int] = Field(None, alias="chatId", description="Chat ID")
    sender_id: Optional[int] = Field(
        None, 
        alias="senderId", 
        description="Sender user ID"
    )
    sender_name: Optional[str] = Field(
        None, 
        alias="senderName", 
        description="Sender name"
    )
    message_type: Optional[str] = Field(
        None, 
        alias="messageType", 
        description="Message type (text, image, file, system)"
    )
    content: Optional[str] = Field(None, description="Message content")
    formatted_content: Optional[str] = Field(
        None, 
        alias="formattedContent", 
        description="Formatted message content"
    )
    reply_to_id: Optional[int] = Field(
        None, 
        alias="replyToId", 
        description="Message ID this is replying to"
    )
    thread_id: Optional[int] = Field(
        None, 
        alias="threadId", 
        description="Thread ID"
    )
    attachments: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="Message attachments"
    )
    mentions: Optional[List[int]] = Field(
        None, 
        description="List of mentioned user IDs"
    )
    reactions: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="Message reactions"
    )
    edited: Optional[bool] = Field(None, description="Edited flag")
    edited_at: Optional[datetime] = Field(
        None, 
        alias="editedAt", 
        description="Edited timestamp"
    )
    deleted: Optional[bool] = Field(None, description="Deleted flag")
    deleted_at: Optional[datetime] = Field(
        None, 
        alias="deletedAt", 
        description="Deleted timestamp"
    )
    read_by: Optional[List[Dict[str, Any]]] = Field(
        None, 
        alias="readBy", 
        description="List of users who read the message"
    )
    
    @property
    def is_deleted(self) -> bool:
        """Check if the message is deleted."""
        return self.deleted is True
    
    @property
    def is_edited(self) -> bool:
        """Check if the message has been edited."""
        return self.edited is True


class ChatParticipant(IDMixin, TimestampMixin):
    """Chat participant model."""
    
    chat_id: Optional[int] = Field(None, alias="chatId", description="Chat ID")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    user_name: Optional[str] = Field(
        None, 
        alias="userName", 
        description="User name"
    )
    role: Optional[str] = Field(
        None, 
        description="Participant role (owner, admin, member)"
    )
    permissions: Optional[List[str]] = Field(
        None, 
        description="Participant permissions"
    )
    joined_at: Optional[datetime] = Field(
        None, 
        alias="joinedAt", 
        description="Joined timestamp"
    )
    last_read_message_id: Optional[int] = Field(
        None, 
        alias="lastReadMessageId", 
        description="Last read message ID"
    )
    last_read_at: Optional[datetime] = Field(
        None, 
        alias="lastReadAt", 
        description="Last read timestamp"
    )
    muted: Optional[bool] = Field(None, description="Muted flag")
    muted_until: Optional[datetime] = Field(
        None, 
        alias="mutedUntil", 
        description="Muted until timestamp"
    )
    active: Optional[bool] = Field(None, description="Active participant flag")
    
    @property
    def is_muted(self) -> bool:
        """Check if the participant is muted."""
        if not self.muted:
            return False
        if not self.muted_until:
            return True
        return datetime.utcnow() < self.muted_until


class ChatInvitation(IDMixin, TimestampMixin):
    """Chat invitation model."""
    
    chat_id: Optional[int] = Field(None, alias="chatId", description="Chat ID")
    inviter_id: Optional[int] = Field(
        None, 
        alias="inviterId", 
        description="Inviter user ID"
    )
    invitee_id: Optional[int] = Field(
        None, 
        alias="inviteeId", 
        description="Invitee user ID"
    )
    invitee_email: Optional[str] = Field(
        None, 
        alias="inviteeEmail", 
        description="Invitee email"
    )
    invitation_code: Optional[str] = Field(
        None, 
        alias="invitationCode", 
        description="Invitation code"
    )
    message: Optional[str] = Field(None, description="Invitation message")
    status: Optional[str] = Field(
        None, 
        description="Invitation status (pending, accepted, declined, expired)"
    )
    expires_at: Optional[datetime] = Field(
        None, 
        alias="expiresAt", 
        description="Expiration timestamp"
    )
    accepted_at: Optional[datetime] = Field(
        None, 
        alias="acceptedAt", 
        description="Accepted timestamp"
    )
    declined_at: Optional[datetime] = Field(
        None, 
        alias="declinedAt", 
        description="Declined timestamp"
    )
    
    @property
    def is_expired(self) -> bool:
        """Check if the invitation is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() >= self.expires_at
    
    @property
    def is_pending(self) -> bool:
        """Check if the invitation is pending."""
        return (
            self.status == "pending" and 
            not self.is_expired and 
            not self.accepted_at and 
            not self.declined_at
        )


class ChatNotification(IDMixin, TimestampMixin):
    """Chat notification model."""
    
    chat_id: Optional[int] = Field(None, alias="chatId", description="Chat ID")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    message_id: Optional[int] = Field(
        None, 
        alias="messageId", 
        description="Message ID"
    )
    notification_type: Optional[str] = Field(
        None, 
        alias="notificationType", 
        description="Notification type"
    )
    title: Optional[str] = Field(None, description="Notification title")
    content: Optional[str] = Field(None, description="Notification content")
    read: Optional[bool] = Field(None, description="Read flag")
    read_at: Optional[datetime] = Field(
        None, 
        alias="readAt", 
        description="Read timestamp"
    )
    delivered: Optional[bool] = Field(None, description="Delivered flag")
    delivered_at: Optional[datetime] = Field(
        None, 
        alias="deliveredAt", 
        description="Delivered timestamp"
    )
    
    @property
    def is_unread(self) -> bool:
        """Check if the notification is unread."""
        return not self.read


class ChatSettings(IDMixin, TimestampMixin):
    """Chat settings model."""
    
    chat_id: Optional[int] = Field(None, alias="chatId", description="Chat ID")
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    notifications_enabled: Optional[bool] = Field(
        None, 
        alias="notificationsEnabled", 
        description="Notifications enabled flag"
    )
    sound_enabled: Optional[bool] = Field(
        None, 
        alias="soundEnabled", 
        description="Sound enabled flag"
    )
    desktop_notifications: Optional[bool] = Field(
        None, 
        alias="desktopNotifications", 
        description="Desktop notifications flag"
    )
    email_notifications: Optional[bool] = Field(
        None, 
        alias="emailNotifications", 
        description="Email notifications flag"
    )
    theme: Optional[str] = Field(None, description="Chat theme")
    language: Optional[str] = Field(None, description="Chat language")
    timezone: Optional[str] = Field(None, description="User timezone")
    custom_settings: Optional[Dict[str, Any]] = Field(
        None, 
        alias="customSettings", 
        description="Custom settings"
    )
