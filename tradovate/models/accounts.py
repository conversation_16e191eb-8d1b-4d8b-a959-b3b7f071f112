"""
Account-related models for the Tradovate SDK.

This module defines models for accounts, cash balances, and trading permissions.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import Field

from .common import BaseModel, IDMixin, TimestampMixin, AuditMixin


class Account(IDMixin, TimestampMixin, AuditMixin):
    """Account model."""
    
    name: Optional[str] = Field(None, description="Account name")
    account_type: Optional[str] = Field(
        None, 
        alias="accountType", 
        description="Account type"
    )
    user_id: Optional[int] = Field(None, alias="userId", description="User ID")
    fcm_id: Optional[int] = Field(None, alias="fcmId", description="FCM ID")
    ib_id: Optional[int] = Field(None, alias="ibId", description="IB ID")
    active: Optional[bool] = Field(None, description="Active account flag")
    clearing_house_id: Optional[int] = Field(
        None, 
        alias="clearingHouseId", 
        description="Clearing house ID"
    )
    risk_category_id: Optional[int] = Field(
        None,
        alias="riskCategoryId",
        description="Risk category ID"
    )
    auto_liq_profile_id: Optional[int] = Field(
        None,
        alias="autoLiqProfileId",
        description="Auto liquidation profile ID"
    )
    margin_account_type: Optional[str] = Field(
        None,
        alias="marginAccountType",
        description="Margin account type"
    )
    legal_status: Optional[str] = Field(
        None,
        alias="legalStatus",
        description="Legal status"
    )


class CashBalance(IDMixin, TimestampMixin):
    """Cash balance model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    cash_balance: Optional[float] = Field(
        None, 
        alias="cashBalance", 
        description="Cash balance"
    )
    open_pl: Optional[float] = Field(None, alias="openPL", description="Open P&L")
    close_pl: Optional[float] = Field(None, alias="closePL", description="Close P&L")
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
    realized_day_pl: Optional[float] = Field(
        None,
        alias="realizedDayPL",
        description="Realized day P&L"
    )
    trade_date: Optional[datetime] = Field(
        None,
        alias="tradeDate",
        description="Trade date"
    )


class MarginSnapshot(IDMixin, TimestampMixin):
    """Margin snapshot model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    initial_margin: Optional[float] = Field(
        None, 
        alias="initialMargin", 
        description="Initial margin"
    )
    maintenance_margin: Optional[float] = Field(
        None, 
        alias="maintenanceMargin", 
        description="Maintenance margin"
    )
    available_cash_balance: Optional[float] = Field(
        None,
        alias="availableCashBalance",
        description="Available cash balance"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
    pending_credit: Optional[float] = Field(
        None,
        alias="pendingCredit",
        description="Pending credit"
    )
    pending_debit: Optional[float] = Field(
        None,
        alias="pendingDebit",
        description="Pending debit"
    )


class TradingPermission(IDMixin, TimestampMixin):
    """Trading permission model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    contract_group_id: Optional[int] = Field(
        None, 
        alias="contractGroupId", 
        description="Contract group ID"
    )
    status: Optional[str] = Field(None, description="Permission status")
    ctc_permission: Optional[bool] = Field(
        None,
        alias="ctcPermission",
        description="CTC permission flag"
    )
    auto_liq: Optional[bool] = Field(
        None,
        alias="autoLiq",
        description="Auto liquidation flag"
    )


class CashBalanceLog(IDMixin, TimestampMixin):
    """Cash balance log model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    delta: Optional[float] = Field(None, description="Balance change amount")
    balance: Optional[float] = Field(None, description="Balance after change")
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
    trade_date: Optional[datetime] = Field(
        None,
        alias="tradeDate",
        description="Trade date"
    )
    comment: Optional[str] = Field(None, description="Log comment")


class AccountRiskStatus(IDMixin, TimestampMixin):
    """Account risk status model."""
    
    account_id: Optional[int] = Field(None, alias="accountId", description="Account ID")
    auto_liq_status: Optional[str] = Field(
        None,
        alias="autoLiqStatus",
        description="Auto liquidation status"
    )
    daily_loss_limit: Optional[float] = Field(
        None,
        alias="dailyLossLimit",
        description="Daily loss limit"
    )
    daily_loss_limit_breach: Optional[bool] = Field(
        None,
        alias="dailyLossLimitBreach",
        description="Daily loss limit breach flag"
    )
    margin_percentage: Optional[float] = Field(
        None,
        alias="marginPercentage",
        description="Margin percentage"
    )
    margin_percentage_alert: Optional[float] = Field(
        None,
        alias="marginPercentageAlert",
        description="Margin percentage alert level"
    )
    liquidation_only: Optional[bool] = Field(
        None,
        alias="liquidationOnly",
        description="Liquidation only flag"
    )


class ContractMargin(IDMixin, TimestampMixin):
    """Contract margin model."""
    
    contract_id: Optional[int] = Field(None, alias="contractId", description="Contract ID")
    initial_margin: Optional[float] = Field(
        None, 
        alias="initialMargin", 
        description="Initial margin"
    )
    maintenance_margin: Optional[float] = Field(
        None, 
        alias="maintenanceMargin", 
        description="Maintenance margin"
    )
    currency_id: Optional[int] = Field(
        None, 
        alias="currencyId", 
        description="Currency ID"
    )
