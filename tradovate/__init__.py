"""
Tradovate Python SDK

A comprehensive Python SDK for the Tradovate API that provides easy access to
trading, market data, account management, and risk management functionality.

Example:
    Basic usage of the Tradovate SDK:

    ```python
    from tradovate import TradovateClient

    # Initialize client
    client = TradovateClient(
        api_key="your_api_key",
        api_secret="your_api_secret",
        environment="demo"  # or "live"
    )

    # Authenticate
    await client.authenticate("username", "password")

    # Get account information
    accounts = await client.accounts.list()

    # Place an order
    order = await client.orders.place_order(
        account_id=accounts[0].id,
        contract_id=12345,  # MES contract
        action="Buy",
        order_type="Market",
        qty=1
    )
    ```
"""

from .client import TradovateClient, TradovateAsyncClient
from .exceptions import (
    TradovateError,
    AuthenticationError,
    RateLimitError,
    APIError,
    ValidationError,
    NetworkError,
    TimeoutError,
    ConfigurationError,
    OrderError,
    MarketDataError,
    WebSocketError,
    ReplayError,
    AccountError,
)
from .models import (
    Account,
    Contract,
    Order,
    Position,
    Fill,
    MarketData,
    User,
    Currency,
    Product,
    Exchange,
    Organization,
    MarketDataSubscription,
)
from .enums import (
    OrderAction,
    OrderType,
    TimeInForce,
    OrderStatus,
    Environment,
)

__version__ = "1.0.0"
__author__ = "Tradovate SDK Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

__all__ = [
    # Main client classes
    "TradovateClient",
    "TradovateAsyncClient",
    
    # Exceptions
    "TradovateError",
    "AuthenticationError",
    "RateLimitError",
    "APIError",
    "ValidationError",
    "NetworkError",
    "TimeoutError",
    "ConfigurationError",
    "OrderError",
    "MarketDataError",
    "WebSocketError",
    "ReplayError",
    "AccountError",
    
    # Models
    "Account",
    "Contract",
    "Order",
    "Position",
    "Fill",
    "MarketData",
    "User",
    "Currency",
    "Product",
    "Exchange",
    "Organization",
    "MarketDataSubscription",
    
    # Enums
    "OrderAction",
    "OrderType", 
    "TimeInForce",
    "OrderStatus",
    "Environment",
    
    # Version info
    "__version__",
    "__author__",
    "__email__",
    "__license__",
]
