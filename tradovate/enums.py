"""
Enumerations used throughout the Tradovate SDK.

This module defines all enums that represent various constants and options
used in the Tradovate API.
"""

from enum import Enum
from typing import Union


class Environment(str, Enum):
    """
    API environment options.

    Values:
        DEMO: Demo/simulation environment for testing (recommended for development)
        LIVE: Live production environment for real trading (use with caution)
    """
    DEMO = "demo"  # Safe testing environment with simulated data
    LIVE = "live"  # Production environment with real money and trades


class OrderAction(str, Enum):
    """
    Order action types.

    Values:
        BUY: Purchase a security (go long)
        SELL: Sell a security (go short or close long position)
    """
    BUY = "Buy"   # Buy/long order
    SELL = "Sell" # Sell/short order


class OrderType(str, Enum):
    """
    Order types supported by Tradovate.

    Values:
        MARKET: Execute immediately at current market price
        LIMIT: Execute only at specified price or better
        STOP_MARKET: Market order triggered when stop price is reached
        STOP_LIMIT: Limit order triggered when stop price is reached
        TRAILING_STOP: Stop that follows price by specified amount
        TRAILING_STOP_LIMIT: Trailing stop with limit price protection
        MIT: Market If Touched - market order when price is touched
        LIT: Limit If Touched - limit order when price is touched
    """
    MARKET = "Market"                           # Immediate execution at market price
    LIMIT = "Limit"                            # Execute at specified price or better
    STOP_MARKET = "StopMarket"                 # Market order when stop price hit
    STOP_LIMIT = "StopLimit"                   # Limit order when stop price hit
    TRAILING_STOP = "TrailingStop"             # Stop that trails price movement
    TRAILING_STOP_LIMIT = "TrailingStopLimit"  # Trailing stop with limit protection
    MIT = "MIT"                                # Market If Touched order
    LIT = "LIT"                                # Limit If Touched order


class TimeInForce(str, Enum):
    """
    Time in force options for orders.

    Values:
        DAY: Order expires at end of trading day
        GTC: Good Till Canceled - remains active until filled or canceled
        GTD: Good Till Date - expires on specified date
        IOC: Immediate or Cancel - fill immediately or cancel unfilled portion
        FOK: Fill or Kill - fill entire order immediately or cancel completely
    """
    DAY = "Day"  # Expires at end of trading day
    GTC = "GTC"  # Good Till Canceled - stays active until filled/canceled
    GTD = "GTD"  # Good Till Date - expires on specified date
    IOC = "IOC"  # Immediate or Cancel - partial fills allowed
    FOK = "FOK"  # Fill or Kill - all or nothing execution


class OrderStatus(str, Enum):
    """
    Order status values.

    Values:
        PENDING: Order submitted but not yet working
        WORKING: Order is active and working in the market
        FILLED: Order has been completely executed
        CANCELLED: Order has been canceled
        REJECTED: Order was rejected by the exchange
        EXPIRED: Order expired due to time in force
    """
    PENDING = "Pending"      # Order submitted, not yet active
    WORKING = "Working"      # Order is active in the market
    FILLED = "Filled"        # Order completely executed
    CANCELLED = "Cancelled"  # Order was canceled
    REJECTED = "Rejected"    # Order rejected by exchange
    EXPIRED = "Expired"      # Order expired
    SUSPENDED = "Suspended"  # Order suspended


class PositionSide(str, Enum):
    """Position side indicators."""
    LONG = "Long"
    SHORT = "Short"
    FLAT = "Flat"


class ContractType(str, Enum):
    """Contract types."""
    FUTURE = "Future"
    OPTION = "Option"
    SPREAD = "Spread"
    FOREX = "Forex"
    CRYPTO = "Crypto"


class MarketDataType(str, Enum):
    """Market data types."""
    QUOTE = "Quote"
    TRADE = "Trade"
    DEPTH = "Depth"
    CHART = "Chart"
    DOM = "DOM"  # Depth of Market


class ChartInterval(str, Enum):
    """Chart interval options."""
    TICK = "Tick"
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"


class SubscriptionType(str, Enum):
    """Subscription types for market data."""
    REAL_TIME = "RealTime"
    DELAYED = "Delayed"
    SNAPSHOT = "Snapshot"


class AlertType(str, Enum):
    """Alert types."""
    PRICE = "Price"
    VOLUME = "Volume"
    POSITION = "Position"
    ORDER = "Order"
    ACCOUNT = "Account"
    SYSTEM = "System"


class RiskParameterType(str, Enum):
    """Risk parameter types."""
    POSITION_LIMIT = "PositionLimit"
    ORDER_LIMIT = "OrderLimit"
    LOSS_LIMIT = "LossLimit"
    MARGIN_LIMIT = "MarginLimit"


class AccountType(str, Enum):
    """Account types."""
    DEMO = "Demo"
    LIVE = "Live"
    SIM = "Sim"


class CurrencyCode(str, Enum):
    """Currency codes."""
    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    JPY = "JPY"
    CAD = "CAD"
    AUD = "AUD"
    CHF = "CHF"


class ExchangeCode(str, Enum):
    """Exchange codes."""
    CME = "CME"
    CBOT = "CBOT"
    NYMEX = "NYMEX"
    COMEX = "COMEX"
    ICE = "ICE"
    EUREX = "EUREX"


class ProductCode(str, Enum):
    """Common product codes."""
    ES = "ES"  # E-mini S&P 500
    MES = "MES"  # Micro E-mini S&P 500
    NQ = "NQ"  # E-mini NASDAQ-100
    MNQ = "MNQ"  # Micro E-mini NASDAQ-100
    YM = "YM"  # E-mini Dow Jones
    MYM = "MYM"  # Micro E-mini Dow Jones
    RTY = "RTY"  # E-mini Russell 2000
    M2K = "M2K"  # Micro E-mini Russell 2000
    CL = "CL"  # Crude Oil
    GC = "GC"  # Gold
    SI = "SI"  # Silver
    ZB = "ZB"  # 30-Year Treasury Bond
    ZN = "ZN"  # 10-Year Treasury Note
    ZF = "ZF"  # 5-Year Treasury Note


class WebSocketEventType(str, Enum):
    """WebSocket event types."""
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    ERROR = "error"
    MESSAGE = "message"
    QUOTE = "quote"
    TRADE = "trade"
    DEPTH = "depth"
    ORDER_UPDATE = "order_update"
    POSITION_UPDATE = "position_update"
    ACCOUNT_UPDATE = "account_update"


class LogLevel(str, Enum):
    """
    Logging levels for SDK output.

    Values:
        DEBUG: Detailed diagnostic information (use for development/debugging)
        INFO: General information about SDK operations (recommended for production)
        WARNING: Warning messages about potential issues
        ERROR: Error messages for failed operations
        CRITICAL: Critical errors that may cause application failure
    """
    DEBUG = "DEBUG"        # Detailed diagnostic info (verbose)
    INFO = "INFO"          # General operational info (recommended)
    WARNING = "WARNING"    # Warning messages
    ERROR = "ERROR"        # Error messages
    CRITICAL = "CRITICAL"  # Critical errors


# Type aliases for convenience
OrderActionType = Union[OrderAction, str]
OrderTypeType = Union[OrderType, str]
TimeInForceType = Union[TimeInForce, str]
EnvironmentType = Union[Environment, str]
