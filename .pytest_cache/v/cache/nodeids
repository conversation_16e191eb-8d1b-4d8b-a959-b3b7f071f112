["tests/integration/test_api_flow.py::TestAccountManagement::test_get_account_by_id", "tests/integration/test_api_flow.py::TestAccountManagement::test_get_cash_balance", "tests/integration/test_api_flow.py::TestAccountManagement::test_get_margin_snapshot", "tests/integration/test_api_flow.py::TestAccountManagement::test_list_accounts", "tests/integration/test_api_flow.py::TestAuthenticationFlow::test_authentication_and_user_info", "tests/integration/test_api_flow.py::TestAuthenticationFlow::test_token_renewal", "tests/integration/test_api_flow.py::TestConcurrentOperations::test_concurrent_account_operations", "tests/integration/test_api_flow.py::TestConcurrentOperations::test_concurrent_contract_lookups", "tests/integration/test_api_flow.py::TestContractManagement::test_find_mes_contracts", "tests/integration/test_api_flow.py::TestContractManagement::test_get_contract_by_id", "tests/integration/test_api_flow.py::TestContractManagement::test_get_exchanges", "tests/integration/test_api_flow.py::TestContractManagement::test_get_products", "tests/integration/test_api_flow.py::TestDataConsistency::test_account_balance_consistency", "tests/integration/test_api_flow.py::TestDataConsistency::test_contract_data_consistency", "tests/integration/test_api_flow.py::TestErrorHandling::test_invalid_contract_id", "tests/integration/test_api_flow.py::TestErrorHandling::test_invalid_order_parameters", "tests/integration/test_api_flow.py::TestErrorHandling::test_unauthorized_operation", "tests/integration/test_api_flow.py::TestOrderManagement::test_complete_order_lifecycle", "tests/integration/test_api_flow.py::TestOrderManagement::test_get_execution_reports", "tests/integration/test_api_flow.py::TestOrderManagement::test_list_orders", "tests/integration/test_api_flow.py::TestPositionManagement::test_get_position_summary", "tests/integration/test_api_flow.py::TestPositionManagement::test_list_positions", "tests/integration/test_api_flow.py::TestRiskManagement::test_get_account_risk_status", "tests/integration/test_api_flow.py::TestRiskManagement::test_get_trading_permissions", "tests/test_client.py::TestTradovateAsyncClient::test_async_client_context_manager", "tests/test_client.py::TestTradovateAsyncClient::test_async_client_initialization", "tests/test_client.py::TestTradovateAsyncClient::test_ensure_http_client", "tests/test_client.py::TestTradovateClient::test_client_context_manager", "tests/test_client.py::TestTradovateClient::test_client_initialization", "tests/test_client.py::TestTradovateClient::test_client_initialization_live_environment", "tests/test_client.py::TestTradovateClient::test_client_initialization_missing_api_key", "tests/test_client.py::TestTradovateClient::test_client_initialization_missing_api_secret", "tests/test_client.py::TestTradovateClient::test_get_headers_with_auth", "tests/test_client.py::TestTradovateClient::test_get_headers_without_auth", "tests/test_client.py::TestTradovateClient::test_is_authenticated_false_expired_token", "tests/test_client.py::TestTradovateClient::test_is_authenticated_false_no_token", "tests/test_client.py::TestTradovateClient::test_is_authenticated_true_valid_token", "tests/unit/test_client.py::TestClientConfiguration::test_from_config_method", "tests/unit/test_client.py::TestClientConfiguration::test_invalid_rate_limit", "tests/unit/test_client.py::TestClientConfiguration::test_invalid_timeout", "tests/unit/test_client.py::TestClientConfiguration::test_rate_limit_configuration", "tests/unit/test_client.py::TestClientConfiguration::test_timeout_configuration", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_authentication_success", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_client_close", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_client_initialization", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_context_manager", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_get_request", "tests/unit/test_client.py::TestTradovateAsyncClient::test_async_post_request", "tests/unit/test_client.py::TestTradovateClient::test_api_modules_initialization", "tests/unit/test_client.py::TestTradovateClient::test_authentication_failure", "tests/unit/test_client.py::TestTradovateClient::test_authentication_network_error", "tests/unit/test_client.py::TestTradovateClient::test_authentication_success", "tests/unit/test_client.py::TestTradovateClient::test_base_url_demo_environment", "tests/unit/test_client.py::TestTradovateClient::test_base_url_live_environment", "tests/unit/test_client.py::TestTradovateClient::test_client_initialization_invalid_api_key", "tests/unit/test_client.py::TestTradovateClient::test_client_initialization_invalid_api_secret", "tests/unit/test_client.py::TestTradovateClient::test_client_initialization_invalid_environment", "tests/unit/test_client.py::TestTradovateClient::test_client_initialization_valid", "tests/unit/test_client.py::TestTradovateClient::test_close_method", "tests/unit/test_client.py::TestTradovateClient::test_get_request", "tests/unit/test_client.py::TestTradovateClient::test_headers_with_auth", "tests/unit/test_client.py::TestTradovateClient::test_headers_without_auth", "tests/unit/test_client.py::TestTradovateClient::test_post_request", "tests/unit/test_client.py::TestTradovateClient::test_websocket_property"]