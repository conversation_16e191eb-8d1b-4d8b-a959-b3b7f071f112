"""
WebSocket Real-Time Data Example

This example demonstrates how to use the Tradovate WebSocket client to receive
real-time market data for MES (Micro E-mini S&P 500) futures contracts.

Features demonstrated:
1. WebSocket connection management
2. Real-time quote subscriptions
3. Trade data streaming
4. Depth of market data
5. Order and position updates
6. Event handling and callbacks
7. Connection recovery and reconnection

WARNING: This is for educational purposes only. Always test with demo accounts first!
"""

import asyncio
import os
import logging
from datetime import datetime
from typing import Dict, Any

from tradovate import TradovateAsyncClient
from tradovate.enums import Environment


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealTimeDataHandler:
    """Handler for real-time market data events."""
    
    def __init__(self):
        self.quote_count = 0
        self.trade_count = 0
        self.depth_count = 0
        self.start_time = datetime.now()
        
    def on_quote(self, quote_data: Dict[str, Any]) -> None:
        """Handle real-time quote updates."""
        self.quote_count += 1
        
        # Extract quote information
        contract_id = quote_data.get('contractId')
        bid = quote_data.get('bid')
        ask = quote_data.get('ask')
        bid_size = quote_data.get('bidSize')
        ask_size = quote_data.get('askSize')
        timestamp = quote_data.get('timestamp')
        
        logger.info(
            f"📊 Quote #{self.quote_count} - Contract: {contract_id} | "
            f"Bid: {bid} ({bid_size}) | Ask: {ask} ({ask_size}) | "
            f"Spread: {ask - bid if bid and ask else 'N/A'}"
        )
        
        # Calculate mid price
        if bid and ask:
            mid_price = (bid + ask) / 2
            logger.info(f"   Mid Price: {mid_price:.2f}")
    
    def on_trade(self, trade_data: Dict[str, Any]) -> None:
        """Handle real-time trade updates."""
        self.trade_count += 1
        
        # Extract trade information
        contract_id = trade_data.get('contractId')
        price = trade_data.get('price')
        size = trade_data.get('size')
        side = trade_data.get('side')
        timestamp = trade_data.get('timestamp')
        
        logger.info(
            f"💹 Trade #{self.trade_count} - Contract: {contract_id} | "
            f"Price: {price} | Size: {size} | Side: {side}"
        )
    
    def on_depth(self, depth_data: Dict[str, Any]) -> None:
        """Handle depth of market updates."""
        self.depth_count += 1
        
        # Extract depth information
        contract_id = depth_data.get('contractId')
        bids = depth_data.get('bids', [])
        asks = depth_data.get('asks', [])
        
        logger.info(f"📈 Depth #{self.depth_count} - Contract: {contract_id}")
        
        # Show top 3 levels
        for i, (bid, ask) in enumerate(zip(bids[:3], asks[:3])):
            bid_price = bid.get('price')
            bid_size = bid.get('size')
            ask_price = ask.get('price')
            ask_size = ask.get('size')
            
            logger.info(
                f"   Level {i+1}: {bid_price} ({bid_size}) | {ask_price} ({ask_size})"
            )
    
    def on_order_update(self, order_data: Dict[str, Any]) -> None:
        """Handle order status updates."""
        order_id = order_data.get('orderId')
        status = order_data.get('status')
        action = order_data.get('action')
        qty = order_data.get('qty')
        
        logger.info(
            f"📋 Order Update - ID: {order_id} | Status: {status} | "
            f"Action: {action} | Qty: {qty}"
        )
    
    def on_position_update(self, position_data: Dict[str, Any]) -> None:
        """Handle position updates."""
        account_id = position_data.get('accountId')
        contract_id = position_data.get('contractId')
        net_pos = position_data.get('netPos')
        bought = position_data.get('bought')
        sold = position_data.get('sold')
        
        logger.info(
            f"📊 Position Update - Account: {account_id} | Contract: {contract_id} | "
            f"Net: {net_pos} | Bought: {bought} | Sold: {sold}"
        )
    
    def on_connect(self, connect_data: Dict[str, Any]) -> None:
        """Handle WebSocket connection."""
        logger.info("🔗 WebSocket connected successfully!")
    
    def on_disconnect(self, disconnect_data: Dict[str, Any]) -> None:
        """Handle WebSocket disconnection."""
        reason = disconnect_data.get('reason', 'Unknown')
        logger.warning(f"🔌 WebSocket disconnected: {reason}")
    
    def on_error(self, error_data: Dict[str, Any]) -> None:
        """Handle WebSocket errors."""
        error = error_data.get('error', 'Unknown error')
        logger.error(f"❌ WebSocket error: {error}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about received data."""
        elapsed = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "elapsed_seconds": elapsed,
            "quotes_received": self.quote_count,
            "trades_received": self.trade_count,
            "depth_updates": self.depth_count,
            "quotes_per_second": self.quote_count / elapsed if elapsed > 0 else 0,
            "trades_per_second": self.trade_count / elapsed if elapsed > 0 else 0,
        }


async def websocket_example():
    """Main WebSocket example function."""
    
    logger.info("🚀 Starting WebSocket Real-Time Data Example")
    
    # Initialize client
    async with TradovateAsyncClient(
        api_key=os.getenv("TRADOVATE_API_KEY"),
        api_secret=os.getenv("TRADOVATE_API_SECRET"),
        environment=Environment.DEMO,  # Always use demo for testing!
        app_id="WebSocketExample",
        app_version="1.0.0"
    ) as client:
        
        try:
            # Authenticate
            logger.info("🔐 Authenticating...")
            await client.authenticate(
                username=os.getenv("TRADOVATE_USERNAME"),
                password=os.getenv("TRADOVATE_PASSWORD")
            )
            logger.info("✅ Authentication successful!")
            
            # Find MES contract
            logger.info("🔍 Finding MES contracts...")
            contracts = await client.contracts.find(name="MES")
            
            if not contracts:
                logger.error("❌ No MES contracts found!")
                return
            
            # Use the first active MES contract
            mes_contract = contracts[0]
            contract_id = mes_contract['id']
            logger.info(f"📋 Using MES contract: {mes_contract.get('contractName')} (ID: {contract_id})")
            
            # Create data handler
            data_handler = RealTimeDataHandler()
            
            # Get WebSocket client
            websocket = client.websocket
            
            # Register event handlers
            websocket.add_event_handler("quote", data_handler.on_quote)
            websocket.add_event_handler("trade", data_handler.on_trade)
            websocket.add_event_handler("depth", data_handler.on_depth)
            websocket.add_event_handler("order_update", data_handler.on_order_update)
            websocket.add_event_handler("position_update", data_handler.on_position_update)
            websocket.add_event_handler("connect", data_handler.on_connect)
            websocket.add_event_handler("disconnect", data_handler.on_disconnect)
            websocket.add_event_handler("error", data_handler.on_error)
            
            # Connect to WebSocket
            logger.info("🔗 Connecting to WebSocket...")
            await websocket.connect()
            
            # Subscribe to real-time data
            logger.info(f"📡 Subscribing to real-time data for contract {contract_id}...")
            
            # Subscribe to quotes
            await websocket.send_message({
                "type": "subscribe",
                "channel": "quotes",
                "contractId": contract_id
            })
            
            # Subscribe to trades
            await websocket.send_message({
                "type": "subscribe", 
                "channel": "trades",
                "contractId": contract_id
            })
            
            # Subscribe to depth of market
            await websocket.send_message({
                "type": "subscribe",
                "channel": "depth",
                "contractId": contract_id,
                "levels": 5  # Top 5 levels
            })
            
            logger.info("✅ Subscriptions active! Receiving real-time data...")
            logger.info("Press Ctrl+C to stop...")
            
            # Run for a specified duration or until interrupted
            try:
                # Start the WebSocket message loop
                websocket_task = asyncio.create_task(websocket.run())
                
                # Run for 60 seconds or until interrupted
                await asyncio.sleep(60)
                
                # Cancel the WebSocket task
                websocket_task.cancel()
                
            except KeyboardInterrupt:
                logger.info("🛑 Interrupted by user")
            
            # Show statistics
            stats = data_handler.get_stats()
            logger.info("\n📊 Session Statistics:")
            logger.info(f"   Duration: {stats['elapsed_seconds']:.1f} seconds")
            logger.info(f"   Quotes received: {stats['quotes_received']}")
            logger.info(f"   Trades received: {stats['trades_received']}")
            logger.info(f"   Depth updates: {stats['depth_updates']}")
            logger.info(f"   Quotes per second: {stats['quotes_per_second']:.2f}")
            logger.info(f"   Trades per second: {stats['trades_per_second']:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Error in WebSocket example: {e}")
        
        finally:
            # Disconnect WebSocket
            if client.websocket.is_connected:
                logger.info("🔌 Disconnecting WebSocket...")
                await client.websocket.disconnect()
            
            logger.info("🎉 WebSocket example completed!")


if __name__ == "__main__":
    # Check for required environment variables
    required_vars = [
        "TRADOVATE_API_KEY",
        "TRADOVATE_API_SECRET", 
        "TRADOVATE_USERNAME",
        "TRADOVATE_PASSWORD"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables before running the example.")
        exit(1)
    
    # Run the example
    asyncio.run(websocket_example())
