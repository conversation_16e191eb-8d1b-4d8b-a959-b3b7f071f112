"""
Risk Management Example

This example demonstrates comprehensive risk management techniques using the
Tradovate Python SDK. It includes:

1. Account risk monitoring
2. Position size calculation
3. Stop loss and take profit management
4. Daily loss limits
5. Position limits enforcement
6. Margin requirement checks
7. Risk alerts and notifications

WARNING: This is for educational purposes only. Always test with demo accounts first!
"""

import asyncio
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from tradovate import TradovateAsyncClient
from tradovate.enums import Environment, OrderAction, OrderType, TimeInForce


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class RiskParameters:
    """Risk management parameters."""
    
    # Account risk limits
    max_account_risk: float = 0.02  # 2% of account value
    max_daily_loss: float = 0.05  # 5% daily loss limit
    max_position_size: int = 10  # Maximum contracts per position
    max_total_positions: int = 3  # Maximum number of open positions
    
    # Trade risk parameters
    max_risk_per_trade: float = 0.01  # 1% risk per trade
    stop_loss_points: float = 20.0  # Default stop loss in points
    take_profit_ratio: float = 2.0  # Risk:Reward ratio (2:1)
    
    # Margin requirements
    min_margin_buffer: float = 0.20  # 20% margin buffer
    max_leverage: float = 10.0  # Maximum leverage
    
    # Position management
    trailing_stop_points: float = 15.0  # Trailing stop distance
    break_even_points: float = 10.0  # Move stop to break-even after this profit


class RiskManager:
    """Comprehensive risk management system."""
    
    def __init__(self, client: TradovateAsyncClient, risk_params: RiskParameters):
        self.client = client
        self.risk_params = risk_params
        self.logger = logging.getLogger(f"{__name__}.RiskManager")
        
        # Risk tracking
        self.daily_pnl = 0.0
        self.daily_start_balance = 0.0
        self.risk_alerts = []
        
    async def initialize(self) -> None:
        """Initialize risk manager with account data."""
        try:
            # Get account information
            accounts = await self.client.accounts.list()
            if not accounts:
                raise ValueError("No accounts found")
            
            self.account = accounts[0]
            self.account_id = self.account['id']
            
            # Get initial balance for daily P&L tracking
            balance = await self.client.accounts.get_cash_balance(self.account_id)
            self.daily_start_balance = balance.get('cashBalance', 0.0)
            
            self.logger.info(f"✅ Risk manager initialized for account {self.account_id}")
            self.logger.info(f"   Starting balance: ${self.daily_start_balance:,.2f}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize risk manager: {e}")
            raise
    
    async def check_account_risk(self) -> Dict[str, Any]:
        """Check overall account risk status."""
        try:
            # Get current balance and positions
            balance = await self.client.accounts.get_cash_balance(self.account_id)
            positions = await self.client.positions.list(account_id=self.account_id)
            
            current_balance = balance.get('cashBalance', 0.0)
            self.daily_pnl = current_balance - self.daily_start_balance
            
            # Calculate risk metrics
            daily_loss_pct = abs(self.daily_pnl) / self.daily_start_balance if self.daily_start_balance > 0 else 0
            open_positions = len([p for p in positions if p.get('netPos', 0) != 0])
            
            # Check risk limits
            risk_status = {
                "account_balance": current_balance,
                "daily_pnl": self.daily_pnl,
                "daily_loss_pct": daily_loss_pct,
                "open_positions": open_positions,
                "max_daily_loss_exceeded": daily_loss_pct > self.risk_params.max_daily_loss,
                "max_positions_exceeded": open_positions >= self.risk_params.max_total_positions,
                "risk_level": "LOW"
            }
            
            # Determine risk level
            if daily_loss_pct > self.risk_params.max_daily_loss * 0.8:
                risk_status["risk_level"] = "HIGH"
            elif daily_loss_pct > self.risk_params.max_daily_loss * 0.5:
                risk_status["risk_level"] = "MEDIUM"
            
            return risk_status
            
        except Exception as e:
            self.logger.error(f"❌ Error checking account risk: {e}")
            return {"error": str(e)}
    
    async def calculate_position_size(
        self,
        contract_id: int,
        entry_price: float,
        stop_loss_price: float
    ) -> Dict[str, Any]:
        """Calculate appropriate position size based on risk parameters."""
        try:
            # Get account balance
            balance = await self.client.accounts.get_cash_balance(self.account_id)
            account_value = balance.get('cashBalance', 0.0)
            
            # Get contract specifications
            contract = await self.client.contracts.get_by_id(contract_id)
            tick_size = contract.get('tickSize', 0.25)
            tick_value = contract.get('tickValue', 12.50)  # Default for MES
            
            # Calculate risk per contract
            price_diff = abs(entry_price - stop_loss_price)
            ticks_at_risk = price_diff / tick_size
            dollar_risk_per_contract = ticks_at_risk * tick_value
            
            # Calculate maximum position size based on risk
            max_dollar_risk = account_value * self.risk_params.max_risk_per_trade
            max_contracts_by_risk = int(max_dollar_risk / dollar_risk_per_contract) if dollar_risk_per_contract > 0 else 0
            
            # Apply position size limits
            max_contracts = min(
                max_contracts_by_risk,
                self.risk_params.max_position_size
            )
            
            return {
                "recommended_size": max_contracts,
                "dollar_risk_per_contract": dollar_risk_per_contract,
                "total_dollar_risk": max_contracts * dollar_risk_per_contract,
                "risk_percentage": (max_contracts * dollar_risk_per_contract) / account_value * 100,
                "tick_size": tick_size,
                "tick_value": tick_value
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating position size: {e}")
            return {"error": str(e)}
    
    async def check_margin_requirements(self, contract_id: int, quantity: int) -> Dict[str, Any]:
        """Check if there's sufficient margin for a trade."""
        try:
            # Get account balance and margin info
            balance = await self.client.accounts.get_cash_balance(self.account_id)
            available_cash = balance.get('cashBalance', 0.0)
            
            # Get contract margin requirements (this would need to be implemented)
            # For now, we'll use estimated values for MES
            estimated_margin_per_contract = 500.0  # Approximate for MES
            
            required_margin = quantity * estimated_margin_per_contract
            margin_buffer = required_margin * self.risk_params.min_margin_buffer
            total_required = required_margin + margin_buffer
            
            return {
                "available_cash": available_cash,
                "required_margin": required_margin,
                "margin_buffer": margin_buffer,
                "total_required": total_required,
                "sufficient_margin": available_cash >= total_required,
                "margin_utilization": total_required / available_cash * 100 if available_cash > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error checking margin requirements: {e}")
            return {"error": str(e)}
    
    async def validate_trade(
        self,
        contract_id: int,
        action: OrderAction,
        quantity: int,
        entry_price: float,
        stop_loss_price: Optional[float] = None
    ) -> Dict[str, Any]:
        """Validate a trade against all risk parameters."""
        validation_result = {
            "approved": False,
            "reasons": [],
            "warnings": [],
            "risk_metrics": {}
        }
        
        try:
            # Check account risk status
            account_risk = await self.check_account_risk()
            
            # Check daily loss limit
            if account_risk.get("max_daily_loss_exceeded"):
                validation_result["reasons"].append("Daily loss limit exceeded")
            
            # Check maximum positions
            if account_risk.get("max_positions_exceeded"):
                validation_result["reasons"].append("Maximum number of positions exceeded")
            
            # Check position size if stop loss is provided
            if stop_loss_price:
                position_calc = await self.calculate_position_size(
                    contract_id, entry_price, stop_loss_price
                )
                
                if quantity > position_calc.get("recommended_size", 0):
                    validation_result["reasons"].append(
                        f"Position size too large. Recommended: {position_calc.get('recommended_size')}"
                    )
                
                validation_result["risk_metrics"]["position_sizing"] = position_calc
            
            # Check margin requirements
            margin_check = await self.check_margin_requirements(contract_id, quantity)
            
            if not margin_check.get("sufficient_margin"):
                validation_result["reasons"].append("Insufficient margin")
            
            validation_result["risk_metrics"]["margin"] = margin_check
            validation_result["risk_metrics"]["account"] = account_risk
            
            # Add warnings for high risk
            if account_risk.get("risk_level") == "HIGH":
                validation_result["warnings"].append("Account risk level is HIGH")
            
            # Approve trade if no blocking reasons
            validation_result["approved"] = len(validation_result["reasons"]) == 0
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"❌ Error validating trade: {e}")
            validation_result["reasons"].append(f"Validation error: {e}")
            return validation_result
    
    async def monitor_positions(self) -> None:
        """Monitor open positions for risk management."""
        try:
            positions = await self.client.positions.list(account_id=self.account_id)
            
            for position in positions:
                net_pos = position.get('netPos', 0)
                if net_pos == 0:
                    continue
                
                contract_id = position.get('contractId')
                avg_price = position.get('avgPrice', 0)
                unrealized_pnl = position.get('unrealizedPnL', 0)
                
                self.logger.info(
                    f"📊 Position - Contract: {contract_id} | "
                    f"Size: {net_pos} | Avg Price: {avg_price} | "
                    f"Unrealized P&L: ${unrealized_pnl:,.2f}"
                )
                
                # Check for risk alerts
                if unrealized_pnl < -1000:  # Example threshold
                    self.risk_alerts.append({
                        "type": "LARGE_LOSS",
                        "contract_id": contract_id,
                        "unrealized_pnl": unrealized_pnl,
                        "timestamp": datetime.now()
                    })
            
        except Exception as e:
            self.logger.error(f"❌ Error monitoring positions: {e}")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get a summary of current risk status."""
        return {
            "daily_pnl": self.daily_pnl,
            "risk_alerts": len(self.risk_alerts),
            "recent_alerts": self.risk_alerts[-5:] if self.risk_alerts else [],
            "risk_parameters": {
                "max_daily_loss": self.risk_params.max_daily_loss,
                "max_position_size": self.risk_params.max_position_size,
                "max_risk_per_trade": self.risk_params.max_risk_per_trade
            }
        }


async def risk_management_example():
    """Main risk management example function."""
    
    logger.info("🛡️ Starting Risk Management Example")
    
    # Initialize client
    async with TradovateAsyncClient(
        api_key=os.getenv("TRADOVATE_API_KEY"),
        api_secret=os.getenv("TRADOVATE_API_SECRET"),
        environment=Environment.DEMO,  # Always use demo for testing!
        app_id="RiskManagementExample",
        app_version="1.0.0"
    ) as client:
        
        try:
            # Authenticate
            logger.info("🔐 Authenticating...")
            await client.authenticate(
                username=os.getenv("TRADOVATE_USERNAME"),
                password=os.getenv("TRADOVATE_PASSWORD")
            )
            logger.info("✅ Authentication successful!")
            
            # Initialize risk manager
            risk_params = RiskParameters()
            risk_manager = RiskManager(client, risk_params)
            await risk_manager.initialize()
            
            # Find MES contract for examples
            contracts = await client.contracts.find(name="MES")
            if not contracts:
                logger.error("❌ No MES contracts found!")
                return
            
            mes_contract = contracts[0]
            contract_id = mes_contract['id']
            
            # Example 1: Check account risk status
            logger.info("\n📊 Checking account risk status...")
            account_risk = await risk_manager.check_account_risk()
            logger.info(f"   Risk Level: {account_risk.get('risk_level')}")
            logger.info(f"   Daily P&L: ${account_risk.get('daily_pnl', 0):,.2f}")
            logger.info(f"   Open Positions: {account_risk.get('open_positions', 0)}")
            
            # Example 2: Calculate position size
            logger.info("\n📏 Calculating position size...")
            entry_price = 4500.0
            stop_loss_price = 4480.0
            
            position_calc = await risk_manager.calculate_position_size(
                contract_id, entry_price, stop_loss_price
            )
            
            logger.info(f"   Entry Price: ${entry_price}")
            logger.info(f"   Stop Loss: ${stop_loss_price}")
            logger.info(f"   Recommended Size: {position_calc.get('recommended_size')} contracts")
            logger.info(f"   Risk per Contract: ${position_calc.get('dollar_risk_per_contract', 0):,.2f}")
            logger.info(f"   Total Risk: ${position_calc.get('total_dollar_risk', 0):,.2f}")
            
            # Example 3: Validate a trade
            logger.info("\n✅ Validating trade...")
            validation = await risk_manager.validate_trade(
                contract_id=contract_id,
                action=OrderAction.BUY,
                quantity=2,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price
            )
            
            logger.info(f"   Trade Approved: {validation['approved']}")
            if validation['reasons']:
                logger.info(f"   Rejection Reasons: {', '.join(validation['reasons'])}")
            if validation['warnings']:
                logger.info(f"   Warnings: {', '.join(validation['warnings'])}")
            
            # Example 4: Monitor positions
            logger.info("\n👀 Monitoring positions...")
            await risk_manager.monitor_positions()
            
            # Example 5: Risk summary
            logger.info("\n📋 Risk Summary:")
            risk_summary = risk_manager.get_risk_summary()
            logger.info(f"   Daily P&L: ${risk_summary['daily_pnl']:,.2f}")
            logger.info(f"   Risk Alerts: {risk_summary['risk_alerts']}")
            logger.info(f"   Max Daily Loss: {risk_summary['risk_parameters']['max_daily_loss']*100:.1f}%")
            
            logger.info("\n🎉 Risk management example completed!")
            
        except Exception as e:
            logger.error(f"❌ Error in risk management example: {e}")


if __name__ == "__main__":
    # Check for required environment variables
    required_vars = [
        "TRADOVATE_API_KEY",
        "TRADOVATE_API_SECRET",
        "TRADOVATE_USERNAME", 
        "TRADOVATE_PASSWORD"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables before running the example.")
        exit(1)
    
    # Run the example
    asyncio.run(risk_management_example())
