"""
Comprehensive API Demo for the Tradovate Python SDK.

This example demonstrates the complete functionality of the Tradovate Python SDK,
showcasing all available API endpoints and features.

WARNING: This is for educational purposes only. Always test with demo accounts first!
"""

import asyncio
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from tradovate import TradovateAsyncClient
from tradovate.enums import Environment, OrderAction, OrderType, TimeInForce


class ComprehensiveAPIDemo:
    """Comprehensive demonstration of all Tradovate SDK functionality."""
    
    def __init__(self):
        self.client: Optional[TradovateAsyncClient] = None
        self.account_id: Optional[int] = None
        self.user_id: Optional[int] = None
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize the client and authenticate."""
        self.logger.info("🚀 Initializing Comprehensive API Demo")
        
        # Initialize client
        self.client = TradovateAsyncClient(
            api_key=os.getenv("TRADOVATE_API_KEY"),
            api_secret=os.getenv("TRADOVATE_API_SECRET"),
            environment=Environment.DEMO,
            app_id="ComprehensiveAPIDemo",
            app_version="1.0.0"
        )
        
        # Authenticate
        auth_response = await self.client.authenticate(
            username=os.getenv("TRADOVATE_USERNAME"),
            password=os.getenv("TRADOVATE_PASSWORD")
        )
        
        self.user_id = auth_response.get('userId')
        self.logger.info(f"✅ Authenticated as user {self.user_id}")
        
        # Get account information
        accounts = await self.client.accounts.list()
        if accounts:
            self.account_id = accounts[0]['id']
            self.logger.info(f"✅ Using account {self.account_id}")
        else:
            raise Exception("No accounts found")
    
    async def demo_authentication_apis(self):
        """Demonstrate authentication APIs."""
        self.logger.info("\n🔐 === Authentication APIs Demo ===")
        
        # Get current user info
        user_info = await self.client.auth.get_user_info()
        self.logger.info(f"User: {user_info.get('name')} ({user_info.get('email', 'No email')})")
        
        # Get user sessions
        sessions = await self.client.users.get_user_sessions(self.user_id)
        self.logger.info(f"Active sessions: {len(sessions)}")
        
        # Get user properties
        properties = await self.client.users.get_user_properties(self.user_id)
        self.logger.info(f"User properties: {len(properties)}")
    
    async def demo_account_apis(self):
        """Demonstrate account management APIs."""
        self.logger.info("\n💰 === Account Management APIs Demo ===")
        
        # Get account details
        account = await self.client.accounts.get(self.account_id)
        self.logger.info(f"Account: {account.get('name')} (Type: {account.get('accountType')})")
        
        # Get cash balance
        cash_balance = await self.client.accounts.get_cash_balance(self.account_id)
        balance = cash_balance.get('cashBalance', 0)
        self.logger.info(f"Cash Balance: ${balance:,.2f}")
        
        # Get margin snapshot
        margin_snapshot = await self.client.accounts.get_margin_snapshot(self.account_id)
        if margin_snapshot:
            initial_margin = margin_snapshot[0].get('initialMargin', 0)
            self.logger.info(f"Initial Margin: ${initial_margin:,.2f}")
        
        # Get trading permissions
        permissions = await self.client.accounts.get_trading_permissions(self.account_id)
        self.logger.info(f"Trading permissions: {len(permissions)}")
        
        # Get cash balance log
        balance_logs = await self.client.cash_balance_log.get_by_account(self.account_id)
        self.logger.info(f"Cash balance log entries: {len(balance_logs)}")
    
    async def demo_contract_apis(self):
        """Demonstrate contract and product APIs."""
        self.logger.info("\n📋 === Contract & Product APIs Demo ===")
        
        # Find MES contracts
        mes_contracts = await self.client.contracts.find(name="MES")
        self.logger.info(f"MES contracts found: {len(mes_contracts)}")
        
        if mes_contracts:
            contract = mes_contracts[0]
            contract_id = contract['id']
            self.logger.info(f"Using contract: {contract.get('name')} (ID: {contract_id})")
            
            # Get contract details
            contract_details = await self.client.contracts.get(contract_id)
            self.logger.info(f"Contract status: {contract_details.get('status')}")
            
            # Get product fee parameters
            try:
                fee_params = await self.client.contracts.get_product_fee_params(contract_id)
                self.logger.info(f"Fee parameters retrieved: {len(fee_params) if isinstance(fee_params, list) else 'Available'}")
            except Exception as e:
                self.logger.warning(f"Fee parameters not available: {e}")
        
        # Get exchanges
        exchanges = await self.client.contracts.get_exchanges()
        self.logger.info(f"Exchanges: {len(exchanges)}")
        
        # Get products
        products = await self.client.products.list()
        self.logger.info(f"Products: {len(products)}")
        
        # Get contract groups
        contract_groups = await self.client.contracts.get_contract_groups()
        self.logger.info(f"Contract groups: {len(contract_groups)}")
    
    async def demo_currency_apis(self):
        """Demonstrate currency APIs."""
        self.logger.info("\n💱 === Currency APIs Demo ===")
        
        # Get all currencies
        currencies = await self.client.currency.list()
        self.logger.info(f"Currencies: {len(currencies)}")
        
        if currencies:
            # Get currency rates
            currency_id = currencies[0]['id']
            rates = await self.client.currency_rate.get_by_currency(currency_id)
            self.logger.info(f"Currency rates for {currencies[0].get('name')}: {len(rates)}")
        
        # Find USD currency
        usd_currencies = await self.client.currency.find("USD")
        if usd_currencies:
            self.logger.info(f"USD currency found: {usd_currencies[0].get('name')}")
    
    async def demo_order_apis(self):
        """Demonstrate order management APIs."""
        self.logger.info("\n📝 === Order Management APIs Demo ===")
        
        # Get recent orders
        orders = await self.client.orders.list(account_id=self.account_id)
        self.logger.info(f"Recent orders: {len(orders)}")
        
        # Get execution reports
        exec_reports = await self.client.orders.get_execution_reports(account_id=self.account_id)
        self.logger.info(f"Execution reports: {len(exec_reports)}")
        
        # Get fills
        fills = await self.client.orders.get_fills(account_id=self.account_id)
        self.logger.info(f"Fills: {len(fills)}")
        
        if fills:
            # Get fill fees for first fill
            fill_id = fills[0]['id']
            fill_fees = await self.client.fill_fee.get_by_fill(fill_id)
            self.logger.info(f"Fill fees for fill {fill_id}: {len(fill_fees)}")
            
            # Get fill pairs
            fill_pairs = await self.client.fill_pair.get_by_fill(fill_id)
            self.logger.info(f"Fill pairs for fill {fill_id}: {len(fill_pairs)}")
        
        # Get order strategies
        strategies = await self.client.orders.list(account_id=self.account_id)
        self.logger.info(f"Order strategies: {len(strategies)}")
    
    async def demo_position_apis(self):
        """Demonstrate position management APIs."""
        self.logger.info("\n📊 === Position Management APIs Demo ===")
        
        # Get positions
        positions = await self.client.positions.list(account_id=self.account_id)
        self.logger.info(f"Positions: {len(positions)}")
        
        for position in positions[:3]:  # Show first 3 positions
            contract_name = position.get('contractName', 'Unknown')
            net_pos = position.get('netPos', 0)
            unrealized_pnl = position.get('unrealizedPnL', 0)
            self.logger.info(f"  {contract_name}: {net_pos} contracts, P&L: ${unrealized_pnl:,.2f}")
    
    async def demo_risk_apis(self):
        """Demonstrate risk management APIs."""
        self.logger.info("\n⚠️  === Risk Management APIs Demo ===")
        
        # Get account risk status
        risk_status = await self.client.risk.get_account_risk_status(self.account_id)
        if risk_status:
            status = risk_status[0] if isinstance(risk_status, list) else risk_status
            self.logger.info(f"Risk status: {status.get('autoLiqStatus', 'Unknown')}")
        
        # Get position limits
        position_limits = await self.client.risk.get_position_limits(self.account_id)
        self.logger.info(f"Position limits: {len(position_limits)}")
        
        # Find MES contract for margin info
        mes_contracts = await self.client.contracts.find(name="MES")
        if mes_contracts:
            contract_id = mes_contracts[0]['id']
            try:
                margin_info = await self.client.risk.get_contract_margin(contract_id)
                if margin_info:
                    margin = margin_info[0] if isinstance(margin_info, list) else margin_info
                    initial_margin = margin.get('initialMargin', 0)
                    self.logger.info(f"MES Initial Margin: ${initial_margin:,.2f}")
            except Exception as e:
                self.logger.warning(f"Margin info not available: {e}")
    
    async def demo_administrative_apis(self):
        """Demonstrate administrative APIs."""
        self.logger.info("\n🏢 === Administrative APIs Demo ===")
        
        # Get clearing houses
        clearing_houses = await self.client.clearing_house.list()
        self.logger.info(f"Clearing houses: {len(clearing_houses)}")
        
        # Get entitlements
        entitlements = await self.client.entitlement.list()
        self.logger.info(f"Entitlements: {len(entitlements)}")
        
        # Get properties
        properties = await self.client.property.list()
        self.logger.info(f"Properties: {len(properties)}")
        
        # Get organizations
        organizations = await self.client.organization.list()
        self.logger.info(f"Organizations: {len(organizations)}")
    
    async def demo_market_data_subscription_apis(self):
        """Demonstrate market data subscription APIs."""
        self.logger.info("\n📡 === Market Data Subscription APIs Demo ===")
        
        # Get market data subscriptions
        subscriptions = await self.client.market_data_subscription.get_by_user(self.user_id)
        self.logger.info(f"Market data subscriptions: {len(subscriptions)}")
        
        # Get second market data subscriptions
        second_subscriptions = await self.client.second_market_data_subscription.get_by_user(self.user_id)
        self.logger.info(f"Second market data subscriptions: {len(second_subscriptions)}")
    
    async def demo_replay_apis(self):
        """Demonstrate replay session APIs."""
        self.logger.info("\n🔄 === Replay Session APIs Demo ===")
        
        # Get replay sessions
        replay_sessions = await self.client.replay_session.get_by_user(self.user_id)
        self.logger.info(f"Replay sessions: {len(replay_sessions)}")
        
        # Note: Creating replay sessions requires specific parameters
        # and may not be available in all environments
        self.logger.info("Replay session creation requires specific time ranges and permissions")
    
    async def demo_command_apis(self):
        """Demonstrate command APIs."""
        self.logger.info("\n⚡ === Command APIs Demo ===")
        
        # Get commands
        commands = await self.client.commands.list(user_id=self.user_id)
        self.logger.info(f"Commands: {len(commands)}")
        
        # Get command reports
        command_reports = await self.client.command_reports.list()
        self.logger.info(f"Command reports: {len(command_reports)}")
    
    async def demo_alert_and_chat_apis(self):
        """Demonstrate alert and chat APIs."""
        self.logger.info("\n🔔 === Alert & Chat APIs Demo ===")
        
        # Get alerts
        alerts = await self.client.alerts.list_alerts(user_id=self.user_id)
        self.logger.info(f"Alerts: {len(alerts)}")
        
        # Get alert signals
        alert_signals = await self.client.alerts.get_alert_signals()
        self.logger.info(f"Alert signals: {len(alert_signals)}")
        
        # Get chats
        chats = await self.client.chat.list_chats(user_id=self.user_id)
        self.logger.info(f"Chats: {len(chats)}")
    
    async def run_comprehensive_demo(self):
        """Run the complete comprehensive demo."""
        try:
            await self.initialize()
            
            # Run all API demonstrations
            await self.demo_authentication_apis()
            await self.demo_account_apis()
            await self.demo_contract_apis()
            await self.demo_currency_apis()
            await self.demo_order_apis()
            await self.demo_position_apis()
            await self.demo_risk_apis()
            await self.demo_administrative_apis()
            await self.demo_market_data_subscription_apis()
            await self.demo_replay_apis()
            await self.demo_command_apis()
            await self.demo_alert_and_chat_apis()
            
            self.logger.info("\n🎉 === Comprehensive API Demo Completed Successfully! ===")
            self.logger.info("All major Tradovate API endpoints have been demonstrated.")
            
        except Exception as e:
            self.logger.error(f"❌ Demo failed: {e}")
            raise
        
        finally:
            if self.client:
                await self.client.close()


async def main():
    """Main function to run the comprehensive demo."""
    print("🚀 Tradovate Python SDK - Comprehensive API Demo")
    print("=" * 60)
    print("⚠️  WARNING: This is for educational purposes only!")
    print("⚠️  Always test with demo accounts first!")
    print("⚠️  This demo covers ALL available API functionality!")
    print("=" * 60)
    
    # Check for required environment variables
    required_vars = [
        "TRADOVATE_API_KEY", 
        "TRADOVATE_API_SECRET", 
        "TRADOVATE_USERNAME", 
        "TRADOVATE_PASSWORD"
    ]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables before running the demo.")
        return
    
    # Run the comprehensive demo
    demo = ComprehensiveAPIDemo()
    await demo.run_comprehensive_demo()


if __name__ == "__main__":
    asyncio.run(main())
