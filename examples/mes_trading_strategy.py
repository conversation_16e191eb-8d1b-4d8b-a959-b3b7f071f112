"""
Advanced MES Trading Strategy Example

This example demonstrates a comprehensive trading strategy for MES (Micro E-mini S&P 500)
futures contracts using the Tradovate Python SDK. It includes:

1. Market data analysis
2. Technical indicators
3. Risk management
4. Position sizing
5. Order management
6. Real-time monitoring

WARNING: This is for educational purposes only. Always test with demo accounts first!
"""

import asyncio
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from tradovate import TradovateAsyncClient
from tradovate.enums import Environment, OrderAction, OrderType, TimeInForce


@dataclass
class TradingConfig:
    """Trading strategy configuration."""
    
    # Account settings
    max_position_size: int = 5  # Maximum number of contracts
    risk_per_trade: float = 0.02  # 2% risk per trade
    max_daily_loss: float = 0.05  # 5% maximum daily loss
    
    # Strategy parameters
    fast_ma_period: int = 10
    slow_ma_period: int = 20
    rsi_period: int = 14
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0
    
    # Risk management
    stop_loss_points: float = 20.0  # Points for stop loss
    take_profit_points: float = 40.0  # Points for take profit
    trailing_stop_points: float = 15.0  # Points for trailing stop
    
    # Timing
    check_interval: int = 60  # Check every 60 seconds
    trading_start_hour: int = 9  # Start trading at 9 AM ET
    trading_end_hour: int = 15  # Stop trading at 3 PM ET


class TechnicalIndicators:
    """Technical analysis indicators."""
    
    @staticmethod
    def simple_moving_average(prices: List[float], period: int) -> Optional[float]:
        """Calculate Simple Moving Average."""
        if len(prices) < period:
            return None
        return sum(prices[-period:]) / period
    
    @staticmethod
    def rsi(prices: List[float], period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index."""
        if len(prices) < period + 1:
            return None
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return None
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2.0) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands."""
        if len(prices) < period:
            return None
        
        sma = sum(prices[-period:]) / period
        variance = sum((price - sma) ** 2 for price in prices[-period:]) / period
        std = variance ** 0.5
        
        return {
            "upper": sma + (std_dev * std),
            "middle": sma,
            "lower": sma - (std_dev * std)
        }


class RiskManager:
    """Risk management system."""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.daily_pnl = 0.0
        self.open_positions = 0
        self.max_drawdown = 0.0
        self.peak_equity = 0.0
    
    def can_open_position(self, account_balance: float) -> bool:
        """Check if we can open a new position."""
        # Check daily loss limit
        if self.daily_pnl <= -account_balance * self.config.max_daily_loss:
            return False
        
        # Check position size limit
        if abs(self.open_positions) >= self.config.max_position_size:
            return False
        
        return True
    
    def calculate_position_size(self, account_balance: float, entry_price: float, stop_loss_price: float) -> int:
        """Calculate position size based on risk management."""
        risk_amount = account_balance * self.config.risk_per_trade
        price_diff = abs(entry_price - stop_loss_price)
        
        if price_diff == 0:
            return 1
        
        # MES contract value is $5 per point
        contract_value_per_point = 5.0
        risk_per_contract = price_diff * contract_value_per_point
        
        position_size = int(risk_amount / risk_per_contract)
        return max(1, min(position_size, self.config.max_position_size))
    
    def update_pnl(self, pnl_change: float):
        """Update daily P&L."""
        self.daily_pnl += pnl_change
    
    def update_positions(self, position_change: int):
        """Update open positions count."""
        self.open_positions += position_change


class MESStrategy:
    """MES futures trading strategy."""
    
    def __init__(self, client: TradovateAsyncClient, config: TradingConfig):
        self.client = client
        self.config = config
        self.risk_manager = RiskManager(config)
        self.indicators = TechnicalIndicators()
        
        # Strategy state
        self.account_id: Optional[int] = None
        self.mes_contract_id: Optional[int] = None
        self.price_history: List[float] = []
        self.current_position = 0
        self.open_orders: List[Dict[str, Any]] = []
        
        # Logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """Initialize the strategy."""
        self.logger.info("Initializing MES trading strategy...")
        
        # Get account information
        accounts = await self.client.accounts.list()
        if not accounts:
            raise Exception("No accounts found")
        
        self.account_id = accounts[0]['id']
        self.logger.info(f"Using account: {accounts[0]['name']} (ID: {self.account_id})")
        
        # Find MES contract
        contracts = await self.client.contracts.find(name="MES")
        mes_contracts = [c for c in contracts if c.get('productName') == 'MES']
        
        if not mes_contracts:
            raise Exception("MES contract not found")
        
        # Use the front month contract
        self.mes_contract_id = mes_contracts[0]['id']
        self.logger.info(f"Using MES contract: {mes_contracts[0]['name']} (ID: {self.mes_contract_id})")
        
        # Get initial position
        positions = await self.client.positions.list(account_id=self.account_id)
        mes_positions = [p for p in positions if p.get('contractId') == self.mes_contract_id]
        
        if mes_positions:
            self.current_position = mes_positions[0].get('netPos', 0)
            self.risk_manager.open_positions = self.current_position
            self.logger.info(f"Current MES position: {self.current_position}")
    
    async def get_market_data(self) -> Optional[float]:
        """Get current market price for MES."""
        try:
            # In a real implementation, this would get live market data
            # For demo purposes, we'll simulate price data
            # You would typically use WebSocket for real-time data
            
            # This is a placeholder - implement actual market data retrieval
            self.logger.warning("Market data retrieval not implemented - using simulated data")
            
            # Simulate price movement around 4500
            import random
            base_price = 4500.0
            if self.price_history:
                base_price = self.price_history[-1]
            
            # Random walk with slight upward bias
            change = random.uniform(-2.0, 2.5)
            new_price = base_price + change
            
            return max(4400.0, min(4600.0, new_price))  # Keep within reasonable range
            
        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            return None
    
    def analyze_market(self) -> Dict[str, Any]:
        """Analyze market conditions and generate signals."""
        if len(self.price_history) < self.config.slow_ma_period:
            return {"signal": "HOLD", "reason": "Insufficient data"}
        
        # Calculate indicators
        fast_ma = self.indicators.simple_moving_average(
            self.price_history, self.config.fast_ma_period
        )
        slow_ma = self.indicators.simple_moving_average(
            self.price_history, self.config.slow_ma_period
        )
        rsi = self.indicators.rsi(self.price_history, self.config.rsi_period)
        bollinger = self.indicators.bollinger_bands(self.price_history)
        
        current_price = self.price_history[-1]
        
        # Generate signals
        signal = "HOLD"
        reason = "No clear signal"
        
        if fast_ma and slow_ma and rsi and bollinger:
            # Bullish conditions
            if (fast_ma > slow_ma and 
                rsi < self.config.rsi_overbought and 
                current_price > bollinger["lower"]):
                signal = "BUY"
                reason = "Fast MA > Slow MA, RSI not overbought, price above lower BB"
            
            # Bearish conditions
            elif (fast_ma < slow_ma and 
                  rsi > self.config.rsi_oversold and 
                  current_price < bollinger["upper"]):
                signal = "SELL"
                reason = "Fast MA < Slow MA, RSI not oversold, price below upper BB"
        
        return {
            "signal": signal,
            "reason": reason,
            "indicators": {
                "fast_ma": fast_ma,
                "slow_ma": slow_ma,
                "rsi": rsi,
                "bollinger": bollinger,
                "current_price": current_price
            }
        }
    
    async def execute_trade(self, signal: str, current_price: float):
        """Execute a trade based on the signal."""
        if signal == "HOLD":
            return
        
        # Get account balance
        balance_info = await self.client.accounts.get_cash_balance(self.account_id)
        account_balance = balance_info.get('cashBalance', 0)
        
        if not self.risk_manager.can_open_position(account_balance):
            self.logger.warning("Risk manager blocked trade")
            return
        
        # Calculate position size and prices
        if signal == "BUY":
            action = OrderAction.BUY
            stop_loss_price = current_price - self.config.stop_loss_points
            take_profit_price = current_price + self.config.take_profit_points
        else:  # SELL
            action = OrderAction.SELL
            stop_loss_price = current_price + self.config.stop_loss_points
            take_profit_price = current_price - self.config.take_profit_points
        
        position_size = self.risk_manager.calculate_position_size(
            account_balance, current_price, stop_loss_price
        )
        
        try:
            # Place main order
            order = await self.client.orders.place_order(
                account_id=self.account_id,
                contract_id=self.mes_contract_id,
                action=action,
                order_type=OrderType.MARKET,
                qty=position_size,
                time_in_force=TimeInForce.DAY,
                is_automated=True
            )
            
            self.logger.info(f"Placed {signal} order: {position_size} contracts at market price")
            
            # Update position tracking
            position_change = position_size if signal == "BUY" else -position_size
            self.current_position += position_change
            self.risk_manager.update_positions(position_change)
            
            # Place stop loss and take profit orders
            await self._place_risk_management_orders(
                action, position_size, stop_loss_price, take_profit_price
            )
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
    
    async def _place_risk_management_orders(
        self, 
        original_action: OrderAction, 
        qty: int, 
        stop_loss_price: float, 
        take_profit_price: float
    ):
        """Place stop loss and take profit orders."""
        try:
            # Opposite action for closing orders
            close_action = OrderAction.SELL if original_action == OrderAction.BUY else OrderAction.BUY
            
            # Stop loss order
            stop_order = await self.client.orders.place_order(
                account_id=self.account_id,
                contract_id=self.mes_contract_id,
                action=close_action,
                order_type=OrderType.STOP_MARKET,
                qty=qty,
                stop_price=stop_loss_price,
                time_in_force=TimeInForce.GTC,
                is_automated=True
            )
            
            # Take profit order
            profit_order = await self.client.orders.place_order(
                account_id=self.account_id,
                contract_id=self.mes_contract_id,
                action=close_action,
                order_type=OrderType.LIMIT,
                qty=qty,
                price=take_profit_price,
                time_in_force=TimeInForce.GTC,
                is_automated=True
            )
            
            self.logger.info(f"Placed stop loss at {stop_loss_price} and take profit at {take_profit_price}")
            
        except Exception as e:
            self.logger.error(f"Error placing risk management orders: {e}")
    
    def is_trading_hours(self) -> bool:
        """Check if we're in trading hours."""
        now = datetime.now()
        return self.config.trading_start_hour <= now.hour < self.config.trading_end_hour
    
    async def run(self):
        """Run the trading strategy."""
        self.logger.info("Starting MES trading strategy...")
        
        try:
            await self.initialize()
            
            while True:
                if not self.is_trading_hours():
                    self.logger.info("Outside trading hours, waiting...")
                    await asyncio.sleep(300)  # Wait 5 minutes
                    continue
                
                # Get market data
                current_price = await self.get_market_data()
                if current_price is None:
                    await asyncio.sleep(self.config.check_interval)
                    continue
                
                # Update price history
                self.price_history.append(current_price)
                if len(self.price_history) > 100:  # Keep last 100 prices
                    self.price_history.pop(0)
                
                # Analyze market
                analysis = self.analyze_market()
                
                self.logger.info(
                    f"Price: {current_price:.2f}, Signal: {analysis['signal']}, "
                    f"Reason: {analysis['reason']}"
                )
                
                # Execute trades
                await self.execute_trade(analysis['signal'], current_price)
                
                # Wait before next iteration
                await asyncio.sleep(self.config.check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("Strategy stopped by user")
        except Exception as e:
            self.logger.error(f"Strategy error: {e}")
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """Clean up resources and close positions if needed."""
        self.logger.info("Cleaning up strategy...")
        
        # Cancel all open orders
        try:
            if self.account_id:
                await self.client.users.cancel_everything(self.account_id)
                self.logger.info("Cancelled all open orders")
        except Exception as e:
            self.logger.error(f"Error cancelling orders: {e}")


async def main():
    """Main function to run the MES trading strategy."""
    
    # Configuration
    config = TradingConfig(
        max_position_size=2,  # Conservative for demo
        risk_per_trade=0.01,  # 1% risk per trade
        max_daily_loss=0.03,  # 3% max daily loss
        check_interval=30,    # Check every 30 seconds for demo
    )
    
    # Initialize client
    async with TradovateAsyncClient(
        api_key=os.getenv("TRADOVATE_API_KEY"),
        api_secret=os.getenv("TRADOVATE_API_SECRET"),
        environment=Environment.DEMO,  # Always use demo for testing!
        app_id="MESStrategy",
        app_version="1.0.0"
    ) as client:
        
        # Authenticate
        await client.authenticate(
            username=os.getenv("TRADOVATE_USERNAME"),
            password=os.getenv("TRADOVATE_PASSWORD")
        )
        
        # Create and run strategy
        strategy = MESStrategy(client, config)
        await strategy.run()


if __name__ == "__main__":
    print("🚀 MES Trading Strategy")
    print("=" * 50)
    print("⚠️  WARNING: This is for educational purposes only!")
    print("⚠️  Always test with demo accounts first!")
    print("⚠️  Trading involves substantial risk of loss!")
    print("=" * 50)
    
    # Check for required environment variables
    required_vars = ["TRADOVATE_API_KEY", "TRADOVATE_API_SECRET", "TRADOVATE_USERNAME", "TRADOVATE_PASSWORD"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        exit(1)
    
    # Run the strategy
    asyncio.run(main())
