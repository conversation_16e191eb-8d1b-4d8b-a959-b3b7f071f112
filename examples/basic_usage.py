"""
Basic usage example for the Tradovate Python SDK.

This example demonstrates how to:
1. Initialize the client
2. Authenticate with the API
3. Get account information
4. Search for MES futures contracts
5. Place a simple order
6. Monitor positions

Note: This is a demo example. Always test with demo accounts first!
"""

import asyncio
import os
from datetime import datetime
from tradovate import TradovateClient, TradovateAsyncClient
from tradovate.enums import Environment, OrderAction, OrderType, TimeInForce


def sync_example():
    """Synchronous example using TradovateClient."""
    
    # Initialize client with demo environment
    client = TradovateClient(
        api_key=os.getenv("TRADOVATE_API_KEY", "your_api_key"),
        api_secret=os.getenv("TRADOVATE_API_SECRET", "your_api_secret"),
        environment=Environment.DEMO,  # Always start with demo!
        app_id="TradovateSDKExample",
        app_version="1.0.0"
    )
    
    try:
        # Step 1: Authenticate
        print("🔐 Authenticating...")
        auth_response = client.authenticate(
            username=os.getenv("TRADOVATE_USERNAME", "your_username"),
            password=os.getenv("TRADOVATE_PASSWORD", "your_password")
        )
        print(f"✅ Authentication successful! User ID: {auth_response.get('userId')}")
        
        # Step 2: Get account information
        print("\n📊 Getting account information...")
        accounts = client.accounts.list()
        if accounts:
            account = accounts[0]  # Use first account
            print(f"✅ Account: {account.get('name')} (ID: {account.get('id')})")
            
            # Get account balance
            balance = client.accounts.get_cash_balance(account['id'])
            print(f"💰 Cash Balance: ${balance.get('cashBalance', 0):,.2f}")
        else:
            print("❌ No accounts found")
            return
        
        # Step 3: Search for MES futures contracts
        print("\n🔍 Searching for MES futures contracts...")
        contracts = client.contracts.find(name="MES")
        
        if contracts:
            # Find the front month MES contract
            mes_contract = None
            for contract in contracts:
                if contract.get('productName') == 'MES':
                    mes_contract = contract
                    break
            
            if mes_contract:
                print(f"✅ Found MES contract: {mes_contract.get('name')} (ID: {mes_contract.get('id')})")
                
                # Step 4: Get current market data
                print("\n📈 Getting market data...")
                try:
                    # Note: This would require market data subscription
                    # For demo purposes, we'll skip actual market data
                    print("📊 Market data would be displayed here (requires subscription)")
                except Exception as e:
                    print(f"⚠️  Market data not available: {e}")
                
                # Step 5: Place a demo order (1 contract)
                print("\n📝 Placing demo order...")
                try:
                    order_data = {
                        "accountId": account['id'],
                        "contractId": mes_contract['id'],
                        "action": OrderAction.BUY,
                        "orderType": OrderType.MARKET,
                        "qty": 1,
                        "timeInForce": TimeInForce.DAY,
                        "isAutomated": True
                    }
                    
                    # Note: Uncomment the following line to actually place an order
                    # order = client.orders.place_order(**order_data)
                    # print(f"✅ Order placed! Order ID: {order.get('id')}")
                    
                    print("📝 Demo order prepared (not submitted):")
                    print(f"   - Action: {order_data['action']}")
                    print(f"   - Type: {order_data['orderType']}")
                    print(f"   - Quantity: {order_data['qty']}")
                    print(f"   - Contract: {mes_contract.get('name')}")
                    
                except Exception as e:
                    print(f"❌ Order placement failed: {e}")
            else:
                print("❌ MES contract not found")
        else:
            print("❌ No contracts found")
        
        # Step 6: Get current positions
        print("\n📍 Getting current positions...")
        positions = client.positions.list(account_id=account['id'])
        
        if positions:
            print(f"✅ Found {len(positions)} position(s):")
            for position in positions:
                print(f"   - {position.get('contractName', 'Unknown')}: {position.get('netPos', 0)} contracts")
        else:
            print("📍 No open positions")
        
        # Step 7: Get recent orders
        print("\n📋 Getting recent orders...")
        orders = client.orders.list(account_id=account['id'])
        
        if orders:
            print(f"✅ Found {len(orders)} recent order(s):")
            for order in orders[:5]:  # Show last 5 orders
                print(f"   - {order.get('action')} {order.get('qty')} {order.get('contractName', 'Unknown')} - {order.get('orderStatus')}")
        else:
            print("📋 No recent orders")
        
        print("\n🎉 Example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Clean up
        client.close()


async def async_example():
    """Asynchronous example using TradovateAsyncClient."""
    
    # Initialize async client
    async with TradovateAsyncClient(
        api_key=os.getenv("TRADOVATE_API_KEY", "your_api_key"),
        api_secret=os.getenv("TRADOVATE_API_SECRET", "your_api_secret"),
        environment=Environment.DEMO,
        app_id="TradovateSDKAsyncExample",
        app_version="1.0.0"
    ) as client:
        
        try:
            # Step 1: Authenticate
            print("🔐 Authenticating (async)...")
            auth_response = await client.authenticate(
                username=os.getenv("TRADOVATE_USERNAME", "your_username"),
                password=os.getenv("TRADOVATE_PASSWORD", "your_password")
            )
            print(f"✅ Authentication successful! User ID: {auth_response.get('userId')}")
            
            # Step 2: Get user info
            print("\n👤 Getting user information...")
            user_info = await client.auth.get_user_info()
            print(f"✅ User: {user_info.get('name')} ({user_info.get('email', 'No email')})")
            
            # Step 3: Concurrent operations example
            print("\n⚡ Running concurrent operations...")
            
            # Run multiple operations concurrently
            accounts_task = client.accounts.list()
            contracts_task = client.contracts.find(name="MES")
            
            accounts, contracts = await asyncio.gather(accounts_task, contracts_task)
            
            print(f"✅ Retrieved {len(accounts)} accounts and {len(contracts)} contracts concurrently")
            
            print("\n🎉 Async example completed successfully!")
            
        except Exception as e:
            print(f"❌ Error: {e}")


def websocket_example():
    """WebSocket example for real-time data."""
    
    print("\n🌐 WebSocket Example")
    print("Note: WebSocket functionality requires additional implementation")
    print("This would demonstrate real-time market data streaming")
    
    # Example of what WebSocket usage would look like:
    """
    client = TradovateClient(...)
    client.authenticate(username, password)
    
    # Subscribe to real-time quotes for MES
    def on_quote(quote_data):
        print(f"MES Quote: Bid={quote_data['bid']}, Ask={quote_data['ask']}")
    
    client.websocket.subscribe_quotes("MES", on_quote)
    client.websocket.start()
    """


if __name__ == "__main__":
    print("🚀 Tradovate Python SDK - Basic Usage Example")
    print("=" * 50)
    
    # Check for required environment variables
    required_vars = ["TRADOVATE_API_KEY", "TRADOVATE_API_SECRET", "TRADOVATE_USERNAME", "TRADOVATE_PASSWORD"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables before running the example.")
        print("Example:")
        print("export TRADOVATE_API_KEY='your_api_key'")
        print("export TRADOVATE_API_SECRET='your_api_secret'")
        print("export TRADOVATE_USERNAME='your_username'")
        print("export TRADOVATE_PASSWORD='your_password'")
        exit(1)
    
    print("\n1️⃣  Running synchronous example...")
    sync_example()
    
    print("\n" + "=" * 50)
    print("2️⃣  Running asynchronous example...")
    asyncio.run(async_example())
    
    print("\n" + "=" * 50)
    print("3️⃣  WebSocket example info...")
    websocket_example()
    
    print("\n✨ All examples completed!")
    print("\n💡 Next steps:")
    print("   - Explore the full API documentation")
    print("   - Try the advanced examples")
    print("   - Implement your trading strategies")
    print("   - Always test with demo accounts first!")
