#!/bin/bash
# Development environment setup script for Tradovate Python SDK

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.8+ is available
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.8+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.8 or later."
        exit 1
    fi
}

# Create virtual environment
setup_venv() {
    print_status "Setting up virtual environment..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_success "Virtual environment activated"
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install the package in development mode with all extras
    pip install -e ".[dev,docs,test]"
    print_success "Dependencies installed"
}

# Setup pre-commit hooks
setup_precommit() {
    print_status "Setting up pre-commit hooks..."
    
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "pre-commit not found, installing..."
        pip install pre-commit
        pre-commit install
        print_success "Pre-commit installed and hooks set up"
    fi
}

# Run initial code quality checks
run_quality_checks() {
    print_status "Running initial code quality checks..."
    
    # Format code
    print_status "Formatting code with black..."
    black tradovate/ tests/ examples/ --check || {
        print_warning "Code formatting issues found. Run 'black tradovate/ tests/ examples/' to fix."
    }
    
    # Sort imports
    print_status "Checking import sorting..."
    isort tradovate/ tests/ examples/ --check-only || {
        print_warning "Import sorting issues found. Run 'isort tradovate/ tests/ examples/' to fix."
    }
    
    # Lint code
    print_status "Linting code..."
    flake8 tradovate/ || {
        print_warning "Linting issues found. Check output above."
    }
    
    # Type checking
    print_status "Running type checks..."
    mypy tradovate/ || {
        print_warning "Type checking issues found. Check output above."
    }
    
    print_success "Code quality checks completed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Run unit tests
    pytest tests/unit/ -v || {
        print_error "Unit tests failed"
        return 1
    }
    
    # Run integration tests if credentials are available
    if [ -n "$TRADOVATE_TEST_API_KEY" ]; then
        print_status "Running integration tests..."
        pytest tests/integration/ -v || {
            print_warning "Integration tests failed"
        }
    else
        print_warning "Integration tests skipped (no test credentials)"
    fi
    
    print_success "Tests completed"
}

# Create development configuration
create_dev_config() {
    print_status "Creating development configuration..."
    
    if [ ! -f ".env.example" ]; then
        cat > .env.example << EOF
# Tradovate API Configuration
TRADOVATE_API_KEY=your_api_key_here
TRADOVATE_API_SECRET=your_api_secret_here
TRADOVATE_USERNAME=your_username_here
TRADOVATE_PASSWORD=your_password_here

# Environment Configuration
TRADOVATE_ENVIRONMENT=demo
TRADOVATE_TIMEOUT=30.0
TRADOVATE_RATE_LIMIT=10
TRADOVATE_LOG_LEVEL=INFO

# Test Configuration
TRADOVATE_TEST_API_KEY=test_api_key
TRADOVATE_TEST_API_SECRET=test_api_secret
TRADOVATE_TEST_USERNAME=test_username
TRADOVATE_TEST_PASSWORD=test_password
EOF
        print_success "Created .env.example file"
    fi
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_warning "Created .env file from example. Please update with your credentials."
    fi
}

# Print setup completion message
print_completion() {
    echo
    print_success "Development environment setup complete!"
    echo
    echo "Next steps:"
    echo "1. Activate the virtual environment: source venv/bin/activate"
    echo "2. Update .env file with your API credentials"
    echo "3. Run tests: pytest"
    echo "4. Start developing!"
    echo
    echo "Useful commands:"
    echo "  pytest                    # Run all tests"
    echo "  pytest tests/unit/        # Run unit tests only"
    echo "  black tradovate/          # Format code"
    echo "  flake8 tradovate/         # Lint code"
    echo "  mypy tradovate/           # Type check"
    echo "  pre-commit run --all-files # Run all pre-commit hooks"
    echo
}

# Main execution
main() {
    echo "Tradovate Python SDK - Development Environment Setup"
    echo "=================================================="
    echo
    
    check_python
    setup_venv
    install_dependencies
    setup_precommit
    create_dev_config
    run_quality_checks
    run_tests
    print_completion
}

# Run main function
main "$@"
