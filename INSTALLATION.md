# Installation Guide

This guide will help you install and set up the Tradovate Python SDK.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- A Tradovate account with API access

## Installation

### Option 1: Install from PyPI (Recommended)

```bash
pip install tradovate-sdk
```

### Option 2: Install from Source

```bash
# Clone the repository
git clone https://github.com/tradovate/tradovate-python-sdk.git
cd tradovate-python-sdk

# Install in development mode
pip install -e .

# Or install with all dependencies
pip install -e ".[dev,docs,examples]"
```

## API Access Setup

### 1. Create a Tradovate Account

1. Visit [Tradovate](https://trader.tradovate.com/welcome)
2. Sign up for an account
3. Complete the account verification process

### 2. Get API Access

To use the Tradovate API, you need:

- **Live Account**: More than $1000 in equity
- **API Access Subscription**: Subscribe to API access in your account
- **API Key and Secret**: Generate these in your account settings

### 3. Generate API Credentials

1. Log into your Tradovate account
2. Go to Account Settings → API Access
3. Generate a new API Key and Secret
4. Save these credentials securely

## Configuration

### Environment Variables

Set up your API credentials as environment variables:

```bash
# Linux/macOS
export TRADOVATE_API_KEY="your_api_key_here"
export TRADOVATE_API_SECRET="your_api_secret_here"
export TRADOVATE_USERNAME="your_username"
export TRADOVATE_PASSWORD="your_password"
export TRADOVATE_ENVIRONMENT="demo"  # or "live"

# Windows (Command Prompt)
set TRADOVATE_API_KEY=your_api_key_here
set TRADOVATE_API_SECRET=your_api_secret_here
set TRADOVATE_USERNAME=your_username
set TRADOVATE_PASSWORD=your_password
set TRADOVATE_ENVIRONMENT=demo

# Windows (PowerShell)
$env:TRADOVATE_API_KEY="your_api_key_here"
$env:TRADOVATE_API_SECRET="your_api_secret_here"
$env:TRADOVATE_USERNAME="your_username"
$env:TRADOVATE_PASSWORD="your_password"
$env:TRADOVATE_ENVIRONMENT="demo"
```

### Configuration File

Alternatively, create a configuration file:

**tradovate.json**
```json
{
  "api_key": "your_api_key_here",
  "api_secret": "your_api_secret_here",
  "environment": "demo",
  "timeout": 30.0,
  "rate_limit": 10,
  "log_level": "INFO"
}
```

Place this file in one of these locations:
- Current working directory
- `~/.tradovate/config.json`
- Custom path specified in your code

## Quick Test

Test your installation with this simple script:

```python
import os
from tradovate import TradovateClient
from tradovate.enums import Environment

# Initialize client
client = TradovateClient(
    api_key=os.getenv("TRADOVATE_API_KEY"),
    api_secret=os.getenv("TRADOVATE_API_SECRET"),
    environment=Environment.DEMO  # Always start with demo!
)

try:
    # Test authentication
    auth_response = client.authenticate(
        username=os.getenv("TRADOVATE_USERNAME"),
        password=os.getenv("TRADOVATE_PASSWORD")
    )
    
    print("✅ Authentication successful!")
    print(f"User ID: {auth_response.get('userId')}")
    
    # Test API call
    accounts = client.accounts.list()
    print(f"✅ Found {len(accounts)} account(s)")
    
except Exception as e:
    print(f"❌ Error: {e}")
finally:
    client.close()
```

## Development Setup

If you're planning to contribute or modify the SDK:

### 1. Clone and Install

```bash
git clone https://github.com/tradovate/tradovate-python-sdk.git
cd tradovate-python-sdk

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode with all dependencies
pip install -e ".[dev]"
```

### 2. Install Pre-commit Hooks

```bash
pre-commit install
```

### 3. Run Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=tradovate --cov-report=html

# Run specific test categories
pytest -m unit
pytest -m integration
```

### 4. Code Quality Checks

```bash
# Format code
black tradovate/
isort tradovate/

# Lint code
flake8 tradovate/
mypy tradovate/

# Run all checks
pre-commit run --all-files
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors

```
AuthenticationError: Authentication failed
```

**Solutions:**
- Verify your API key and secret are correct
- Check that your username and password are correct
- Ensure you're using the correct environment (demo vs live)
- Verify your account has API access enabled

#### 2. Rate Limiting

```
RateLimitError: Rate limit exceeded
```

**Solutions:**
- Reduce the frequency of API calls
- Implement proper rate limiting in your code
- Use the built-in rate limiter: `TradovateClient(rate_limit=5)`

#### 3. Network Errors

```
NetworkError: Connection failed
```

**Solutions:**
- Check your internet connection
- Verify firewall settings
- Try using a different network
- Check Tradovate API status

#### 4. Import Errors

```
ImportError: No module named 'tradovate'
```

**Solutions:**
- Ensure the SDK is installed: `pip install tradovate-sdk`
- Check your Python environment
- Verify virtual environment is activated

### Getting Help

If you encounter issues:

1. **Check the Documentation**: https://tradovate-python-sdk.readthedocs.io
2. **Search Issues**: https://github.com/tradovate/tradovate-python-sdk/issues
3. **Create an Issue**: Provide detailed error messages and steps to reproduce
4. **Join Discussions**: https://github.com/tradovate/tradovate-python-sdk/discussions

## Next Steps

Once you have the SDK installed and configured:

1. **Read the Documentation**: Familiarize yourself with the API
2. **Try the Examples**: Run the provided example scripts
3. **Start Small**: Begin with simple operations like getting account info
4. **Use Demo Environment**: Always test with demo accounts first
5. **Implement Risk Management**: Never trade without proper risk controls

## Security Best Practices

- **Never commit credentials** to version control
- **Use environment variables** or secure configuration files
- **Rotate API keys** regularly
- **Use demo environment** for testing
- **Implement proper error handling**
- **Monitor API usage** and costs

## Support

For additional support:

- **Email**: <EMAIL>
- **Documentation**: https://tradovate-python-sdk.readthedocs.io
- **GitHub Issues**: https://github.com/tradovate/tradovate-python-sdk/issues
- **Tradovate Support**: https://tradovate.com/support
